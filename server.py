#!/usr/bin/env python3
"""
Clinical Trials Analysis Web Server
===================================

Flask-based web server providing REST API for clinical trials analysis system.

Key Features:
- Real-time progress tracking for data extraction and AI analysis
- Integration with Ollama for local LLM processing
- Drug-specific data organization and management
- Progressive filtering with consistent discovery-to-filtering logic
- No hardcoded AI models - uses only user-selected models

API Endpoints:
- /api/models: Get available Ollama models
- /api/drug-folders: Get available drug datasets
- /api/load-studies: Load studies for selected drug
- /api/discover-terms: Discover related terms in study sections
- /api/filter-studies: Apply progressive filtering
- /api/extract-data: Extract new clinical trial data
- /api/analyze-studies: Run AI analysis on filtered studies

Author: Clinical Trials Analysis System
Version: 2.0 - Clean Architecture
"""

# Standard library imports
import json
import os
import subprocess
import sys
import threading
import time
from datetime import datetime

# Third-party imports
import requests
from flask import Flask, send_file, jsonify, request

# ============================================================================
# MODULE IMPORTS WITH ERROR HANDLING
# ============================================================================

# Import analysis functions
try:
    from meta_analysis import discover_terms_in_section, filter_studies_by_criteria, filter_studies_by_criteria_ai
    from ai_model_setup import get_available_models, validate_model, test_model_connection
    print("✅ Meta-analysis and AI model functions loaded")
except ImportError as e:
    print(f"Warning: Could not import analysis functions: {e}")
    discover_terms_in_section = None
    filter_studies_by_criteria = None
    filter_studies_by_criteria_ai = None
    get_available_models = None
    validate_model = None
    test_model_connection = None

# Import drug class detection
try:
    from find_drug_class import DrugClassDetector
    enhanced_detector = DrugClassDetector()
    print("✅ Enhanced drug class detector loaded with LLM support")
except ImportError as e:
    print(f"Warning: Could not import drug class detector: {e}")
    enhanced_detector = None

# Import clinical trials data extraction
try:
    from retrieve_studies import ClinicalTrialsModuleExtractor
    print("✅ Clinical trials module extractor loaded")
except ImportError as e:
    print(f"Warning: Could not import retrieve_studies: {e}")
    ClinicalTrialsModuleExtractor = None

# Initialize Flask application
app = Flask(__name__)

# ============================================================================
# GLOBAL STATE MANAGEMENT
# ============================================================================

# Global progress tracking for real-time updates
progress_data = {
    'status': 'idle',           # Current operation status: 'idle', 'running', 'completed', 'error'
    'operation': 'none',        # Current operation type: 'extract', 'analyze', 'batch_extract'
    'progress': 0,              # Progress percentage (0-100)
    'message': 'Ready',         # Current status message
    'terminal_output': [],      # List of terminal output messages
    'results': [],              # Analysis results
    'study_count': 0,           # Number of studies being processed
    'project_name': '',         # Current project name
    'drugs': [],                # List of drugs being processed
    'current_drug_index': 0     # Index of currently processing drug
}

# Global process tracking for background operations
current_process = None

# ============================================================================
# WEB INTERFACE ROUTES
# ============================================================================

@app.route('/')
def home():
    """Serve the main web interface"""
    return send_file('clinical_trials_web_ui.html')

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def validate_request_data(data, required_fields):
    """
    Validate that request data contains all required fields

    Args:
        data (dict): Request data
        required_fields (list): List of required field names

    Returns:
        tuple: (is_valid, error_message)
    """
    if not data:
        return False, "No data provided"

    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        return False, f"Missing required fields: {', '.join(missing_fields)}"

    return True, None

def create_error_response(message, status_code=400):
    """Create standardized error response"""
    return jsonify({'success': False, 'message': message}), status_code

def create_success_response(data=None, message="Success"):
    """Create standardized success response"""
    response = {'success': True, 'message': message}
    if data:
        response.update(data)
    return jsonify(response)

# ============================================================================
# DRUG CLASS DETECTION API
# ============================================================================

@app.route('/api/detect-drug-class', methods=['POST'])
def detect_drug_class_api():
    """
    Detect if input is a single drug or drug class and return available options with LLM support

    Expected JSON payload:
    {
        "drug_input": "canagliflozin",
        "ai_config": {
            "model": "llama2",
            "temperature": 0.1,
            "maxTokens": 2048
        }
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        is_valid, error_msg = validate_request_data(data, ['drug_input'])
        if not is_valid:
            return create_error_response(error_msg)

        drug_input = data['drug_input'].strip()

        # Check if enhanced detector is available
        if not enhanced_detector:
            return create_error_response(
                'Enhanced drug class detector with LLM is required. Please configure your AI model in the AI Model tab.'
            )

        # Extract AI configuration
        ai_config = data.get('ai_config', {})
        selected_model = ai_config.get('model')
        temperature = ai_config.get('temperature', 0.1)
        max_tokens = ai_config.get('maxTokens', 2048)

        if not selected_model:
            return create_error_response(
                'No AI model selected. Please configure your AI model in the AI Model tab first.'
            )

        print(f"🔍 Drug class detection: '{drug_input}' using model: {selected_model}")

        # Process drug class request
        result = enhanced_detector.process_drug_class_request(
            drug_input,
            selected_model=selected_model,
            temperature=temperature,
            max_tokens=max_tokens
        )

        if result['success']:
            return create_success_response({
                'is_drug_class': True,
                'drug_class': result['drug_class'],
                'description': result['description'],
                'reasoning': result.get('reasoning', ''),
                'confidence': result.get('confidence', 0),
                'source': 'llm',
                'drugs': result['drugs_with_studies'],
                'drugs_with_actual_studies': result['drugs_with_actual_studies']
            }, result['message'])
        else:
            return create_error_response(result['message'])

    except Exception as e:
        print(f"❌ Error in detect_drug_class_api: {e}")
        return create_error_response(f"LLM processing failed: {str(e)}. Please check your AI model configuration.")

def get_mock_study_count(drug_name):
    """
    Generate mock study count for testing purposes

    Args:
        drug_name (str): Name of the drug

    Returns:
        int: Mock study count
    """
    # Simple hash-based mock count for consistency
    return abs(hash(drug_name.lower())) % 50 + 10

@app.route('/api/get-study-count', methods=['POST'])
def get_study_count_api():
    """
    Get study count for a specific drug

    Expected JSON payload:
    {
        "drug_name": "canagliflozin"
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        is_valid, error_msg = validate_request_data(data, ['drug_name'])
        if not is_valid:
            return create_error_response(error_msg)

        drug_name = data['drug_name'].strip()

        # Try to get actual study count
        try:
            from find_studies import search_and_filter_studies
            results = search_and_filter_studies(drug_name)

            if isinstance(results, dict) and 'total_studies' in results:
                study_count = results['total_studies']
            elif isinstance(results, list):
                study_count = len(results)
            else:
                study_count = get_mock_study_count(drug_name)

        except Exception as e:
            print(f"⚠️ Could not get actual study count for {drug_name}: {e}")
            study_count = get_mock_study_count(drug_name)

        return create_success_response({
            'drug_name': drug_name,
            'study_count': study_count
        })

    except Exception as e:
        print(f"❌ Error in get_study_count_api: {e}")
        return create_error_response(str(e))

# ============================================================================
# PROJECT MANAGEMENT API
# ============================================================================

def sanitize_project_name(project_name):
    """
    Sanitize project name by removing invalid characters

    Args:
        project_name (str): Raw project name

    Returns:
        str: Sanitized project name safe for filesystem
    """
    import re
    # Replace invalid characters with underscores
    sanitized = re.sub(r'[^\w\-_.]', '_', project_name.strip())
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    # Remove leading/trailing underscores
    return sanitized.strip('_')

@app.route('/api/create-project', methods=['POST'])
def create_project():
    """
    Create a new project folder for organizing clinical trials data

    Expected JSON payload:
    {
        "project_name": "diabetes_study_2024"
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        is_valid, error_msg = validate_request_data(data, ['project_name'])
        if not is_valid:
            return create_error_response(error_msg)

        # Sanitize project name
        project_name = sanitize_project_name(data['project_name'])

        if not project_name:
            return create_error_response('Invalid project name after sanitization')

        # Create project directory
        project_path = os.path.join('output', project_name)
        os.makedirs(project_path, exist_ok=True)

        print(f"📁 Created project folder: {project_path}")

        return create_success_response({
            'project_name': project_name,
            'project_path': project_path
        }, f'Project folder created: {project_name}')

    except Exception as e:
        print(f"❌ Error in create_project: {e}")
        return create_error_response(str(e))

@app.route('/api/delete-project', methods=['POST'])
def delete_project():
    """
    Delete a project folder and all its contents

    Expected JSON payload:
    {
        "project_name": "diabetes_study_2024"
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        is_valid, error_msg = validate_request_data(data, ['project_name'])
        if not is_valid:
            return create_error_response(error_msg)

        # Sanitize project name
        project_name = sanitize_project_name(data['project_name'])

        if not project_name:
            return create_error_response('Invalid project name after sanitization')

        # Delete project directory
        project_path = os.path.join('output', project_name)

        if os.path.exists(project_path):
            import shutil
            shutil.rmtree(project_path)
            print(f"🗑️ Deleted project folder: {project_path}")
            message = f'Project folder deleted: {project_name}'
        else:
            print(f"⚠️ Project folder does not exist: {project_path}")
            message = f'Project folder does not exist: {project_name}'

        return create_success_response(message=message)

    except Exception as e:
        print(f"❌ Error in delete_project: {e}")
        return create_error_response(str(e))

# ============================================================================
# DATA EXTRACTION API
# ============================================================================

@app.route('/api/batch-extract', methods=['POST'])
def batch_extract():
    """
    Start batch extraction for multiple drugs in a named project folder

    Expected JSON payload:
    {
        "project_name": "diabetes_study_2024",
        "drugs": ["canagliflozin", "dapagliflozin"]
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        is_valid, error_msg = validate_request_data(data, ['project_name', 'drugs'])
        if not is_valid:
            return create_error_response(error_msg)

        project_name = data['project_name']
        drugs = data['drugs']

        if not isinstance(drugs, list) or len(drugs) == 0:
            return create_error_response('Drugs must be a non-empty list')

        # Sanitize project name
        project_name = sanitize_project_name(project_name)

        if not project_name:
            project_name = f'project_{datetime.now().strftime("%Y_%m_%d_%H_%M")}'

        # Create project directory
        project_path = os.path.join('output', project_name)
        os.makedirs(project_path, exist_ok=True)

        print(f"🚀 Starting batch extraction for {len(drugs)} drugs in project: {project_name}")

        # Start batch extraction process
        start_batch_extraction_process(project_name, drugs)

        return create_success_response({
            'project_name': project_name
        }, f'Started batch extraction for {len(drugs)} drugs in project: {project_name}')

    except Exception as e:
        print(f"Error in batch_extract: {e}")
        return jsonify({'success': False, 'message': str(e)})

def get_mock_study_count(drug_name):
    """Get mock study count for demonstration (replace with actual API call)"""
    # Mock data - in reality, this would call the find_studies script
    mock_counts = {
        'Canagliflozin': 156,
        'Dapagliflozin': 142,
        'Empagliflozin': 178,
        'Ertugliflozin': 89,
        'Semaglutide': 234,
        'Liraglutide': 198,
        'Dulaglutide': 167,
        'Exenatide': 145
    }
    return mock_counts.get(drug_name, 50)  # Default to 50 if not found

def get_actual_study_count(drug_name):
    """Get actual study count using the find_studies script"""
    try:
        # Import and use the existing find_studies functionality
        from find_studies import search_and_filter_studies
        # This would need to be modified to return just the count
        return get_mock_study_count(drug_name)  # For now, use mock data
    except Exception as e:
        print(f"Error getting actual study count: {e}")
        return get_mock_study_count(drug_name)

def start_batch_extraction_process(project_name, drugs):
    """Start the batch extraction process in background"""
    global current_process, progress_data

    # Update progress data
    progress_data.update({
        'status': 'running',
        'operation': 'batch_extract',
        'progress': 0,
        'message': f'Starting batch extraction for {len(drugs)} drugs...',
        'terminal_output': [f'🚀 Starting batch extraction in project: {project_name}'],
        'project_name': project_name,
        'drugs': drugs,
        'current_drug_index': 0
    })

    # Start background thread for batch extraction
    def batch_extract_worker():
        try:
            def progress_callback(message, progress=None, total=None, nct_id=None, current=None):
                """Callback function to update progress"""
                if progress is not None:
                    progress_data['progress'] = progress

                # Format message with NCT progress if available
                if nct_id and current and total:
                    formatted_message = f"{message} - {nct_id} ({current}/{total})"
                    progress_data['message'] = formatted_message
                    progress_data['terminal_output'].append(formatted_message)
                else:
                    progress_data['message'] = message
                    progress_data['terminal_output'].append(message)

            # Use the module extractor if available
            if ClinicalTrialsModuleExtractor:
                extractor = ClinicalTrialsModuleExtractor()
                extractor.set_progress_callback(progress_callback)

                # Process each drug
                all_success = True
                for drug in drugs:
                    success = extractor.process_drug(drug, f"output/{project_name}")
                    if not success:
                        all_success = False

                if all_success:
                    progress_data.update({
                        'status': 'completed',
                        'progress': 100,
                        'message': f'✅ Extraction completed for {", ".join(drugs)}'
                    })
                else:
                    progress_data.update({
                        'status': 'error',
                        'message': 'Module extraction failed for one or more drugs'
                    })
            else:
                print(f"🔧 DEBUG: ClinicalTrialsModuleExtractor is NOT available")
                # Fallback to mock extraction
                fallback_batch_extraction(project_name, drugs)

        except Exception as e:
            progress_data.update({
                'status': 'error',
                'message': f'Batch extraction failed: {str(e)}'
            })
            progress_data['terminal_output'].append(f'❌ Error: {str(e)}')

    thread = threading.Thread(target=batch_extract_worker)
    thread.daemon = True
    thread.start()

def fallback_batch_extraction(project_name, drugs):
    """Fallback batch extraction using mock data"""
    project_path = os.path.join('output', project_name)

    for i, drug in enumerate(drugs):
        progress_data['current_drug_index'] = i
        progress_data['progress'] = (i / len(drugs)) * 100
        progress_data['message'] = f'Extracting data for {drug}...'
        progress_data['terminal_output'].append(f'📥 Processing {drug} ({i+1}/{len(drugs)})')

        # Create drug-specific folder
        drug_folder = os.path.join(project_path, drug.lower().replace(' ', '_'))
        os.makedirs(drug_folder, exist_ok=True)

        # Extract data for this drug (mock)
        extract_drug_data(drug, drug_folder)

        progress_data['terminal_output'].append(f'✅ Completed {drug}')

    progress_data.update({
        'status': 'completed',
        'progress': 100,
        'message': f'✅ Extraction completed for {", ".join(drugs)}'
    })
    progress_data['terminal_output'].append('🎉 All extractions completed!')

def extract_drug_data(drug_name, output_folder):
    """Extract data for a single drug (placeholder implementation)"""
    import time

    # Mock extraction process
    time.sleep(2)  # Simulate extraction time

    # Create mock JSON files
    study_details = {'drug': drug_name, 'type': 'study_details', 'extracted_at': datetime.now().isoformat()}
    researcher_view = {'drug': drug_name, 'type': 'researcher_view', 'extracted_at': datetime.now().isoformat()}
    results_posted = {'drug': drug_name, 'type': 'results_posted', 'extracted_at': datetime.now().isoformat()}

    # Save JSON files
    with open(os.path.join(output_folder, 'study_details.json'), 'w') as f:
        json.dump(study_details, f, indent=2)

    with open(os.path.join(output_folder, 'researcher_view.json'), 'w') as f:
        json.dump(researcher_view, f, indent=2)

    with open(os.path.join(output_folder, 'results_posted.json'), 'w') as f:
        json.dump(results_posted, f, indent=2)

    # Convert to text and CSV (placeholder)
    convert_json_to_text(output_folder)
    convert_adverse_events_to_csv(output_folder)

def convert_json_to_text(folder_path):
    """Convert JSON files to text format"""
    # Placeholder - would use the existing json2text script
    with open(os.path.join(folder_path, 'study_summary.txt'), 'w') as f:
        f.write(f"Study summary for folder: {folder_path}\n")
        f.write("Generated from JSON files\n")

def convert_adverse_events_to_csv(folder_path):
    """Convert adverse events to CSV format"""
    # Placeholder - would use the existing json2csv script
    import csv
    with open(os.path.join(folder_path, 'adverse_events.csv'), 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Event', 'Type', 'Count', 'Group'])
        writer.writerow(['Sample Event', 'Serious', '5', 'Treatment'])
        writer.writerow(['Sample Event 2', 'Non-serious', '12', 'Control'])

# ============================================================================
# AI MODEL INTEGRATION API
# ============================================================================

@app.route('/api/models')
def get_models():
    """
    Get available Ollama models with size information

    Returns:
        JSON response with available models or error message
    """
    if not get_available_models:
        return create_error_response('AI model setup not available')

    try:
        result = get_available_models()
        return jsonify(result)
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return create_error_response(f"Failed to get models: {str(e)}")

@app.route('/api/extract', methods=['POST'])
def extract():
    """Handle extraction request"""
    global progress_data

    data = request.json
    drug = data.get('drug', '').strip()

    print(f"🚀 Starting extraction: drug={drug}")

    if not drug:
        return jsonify({'error': 'Drug name required'}), 400

    # Reset progress
    progress_data = {
        'status': 'running',
        'operation': 'extract',
        'progress': 0,
        'message': f'Starting search for {drug}...',
        'terminal_output': [f'📁 Clinical Trials Data Extraction'],
        'results': [],
        'study_count': 0,
        'drug_name': drug
    }

    # Start extraction in background
    thread = threading.Thread(target=run_extraction, args=(drug,))
    thread.daemon = True
    thread.start()

    return jsonify({'status': 'success', 'message': 'Extraction started'})

def count_nct_studies(folder_path):
    """
    Count NCT studies in a folder (excluding CSV and JSON files)

    Args:
        folder_path (str): Path to folder to scan

    Returns:
        int: Number of NCT study folders found
    """
    try:
        return len([d for d in os.listdir(folder_path)
                   if os.path.isdir(os.path.join(folder_path, d)) and d.startswith('NCT')])
    except Exception:
        return 0

@app.route('/api/drug-folders', methods=['GET'])
def get_drug_folders():
    """
    Get list of available drug folders with nested structure support

    Returns:
        JSON response with folders list containing drug folders with study counts
    """
    try:
        output_dir = 'output'
        if not os.path.exists(output_dir):
            return jsonify({'folders': []})

        folders = []

        for item in os.listdir(output_dir):
            item_path = os.path.join(output_dir, item)
            if not os.path.isdir(item_path):
                continue

            # Check if this is a project folder with nested drug folders
            nested_drugs = []
            for subitem in os.listdir(item_path):
                subitem_path = os.path.join(item_path, subitem)
                if os.path.isdir(subitem_path):
                    study_count = count_nct_studies(subitem_path)
                    if study_count > 0:  # Only include folders with actual studies
                        nested_drugs.append({
                            'name': f"{item}/{subitem}",
                            'display_name': f"{item}/{subitem} ({study_count} studies)",
                            'study_count': study_count
                        })

            # If we found nested drug folders, add them
            if nested_drugs:
                folders.extend(nested_drugs)
            else:
                # This is a direct drug folder (old format)
                study_count = count_nct_studies(item_path)
                if study_count > 0:  # Only include folders with actual studies
                    folders.append({
                        'name': item,
                        'display_name': f"{item} ({study_count} studies)",
                        'study_count': study_count
                    })

        print(f"📁 Found {len(folders)} drug folders with studies")
        return jsonify({'folders': folders})

    except Exception as e:
        print(f"❌ Error getting drug folders: {e}")
        return create_error_response(str(e))

@app.route('/api/load-studies', methods=['POST'])
def load_studies():
    """Load studies from a specific drug folder"""
    global progress_data

    data = request.json
    drug_folder = data.get('drug_folder', '').strip()

    if not drug_folder:
        return jsonify({'error': 'Drug folder required'}), 400

    studies_dir = os.path.join('output', drug_folder)
    if not os.path.exists(studies_dir):
        return jsonify({'error': f'Drug folder "{drug_folder}" not found'}), 404

    # Count studies
    study_count = len([d for d in os.listdir(studies_dir) if os.path.isdir(os.path.join(studies_dir, d))])

    if study_count == 0:
        return jsonify({'error': f'No studies found in "{drug_folder}" folder'}), 404

    # Update progress data to track current drug
    progress_data['drug_name'] = drug_folder
    progress_data['study_count'] = study_count

    return jsonify({
        'success': True,
        'message': f'Loaded {study_count} studies from {drug_folder}',
        'study_count': study_count,
        'drug_name': drug_folder
    })

@app.route('/api/discover-terms', methods=['POST'])
def discover_terms():
    """Discover related terms in a specific section"""
    try:
        data = request.get_json()
        studies_dir = data.get('studies_dir', 'studies')
        section = data.get('section', '')
        search_term = data.get('search_term', '')
        filtered_studies = data.get('filtered_studies', None)  # Optional list of study IDs to search within

        # AI configuration (optional)
        ai_config = data.get('ai_config', {})
        use_ai = ai_config.get('use_ai', False)
        llm_model = ai_config.get('model', None)

        if not section or not search_term:
            return jsonify({'error': 'Section and search term required'}), 400

        if not discover_terms_in_section:
            return jsonify({'error': 'Analysis functions not available'}), 500

        # Validate AI configuration if AI is requested
        if use_ai and not llm_model:
            return jsonify({'error': 'LLM model required for AI-powered term discovery'}), 400

        # Discover terms (with optional filtering and AI)
        results = discover_terms_in_section(
            studies_dir,
            section,
            search_term,
            filtered_studies,
            use_ai=use_ai,
            llm_model=llm_model
        )

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/enhanced-discover-terms', methods=['POST'])
def enhanced_discover_terms():
    """
    SIMPLE SEARCH:
    - ADVERSE EVENTS: AI-powered search (complex, finds synonyms)
    - EVERYTHING ELSE: Simple exact search (like Excel FIND)
    """
    try:
        data = request.get_json()
        studies_dir = data.get('studies_dir', '')
        category = data.get('category', '')
        search_term = data.get('search_term', '')
        filtered_studies = data.get('filtered_studies', None)

        if not studies_dir or not category or not search_term:
            return jsonify({'error': 'Studies directory, category, and search term required'}), 400

        if category == 'adverse_events':
            # AI-POWERED SEARCH for adverse events
            import pandas as pd
            import os

            ae_file = os.path.join(studies_dir, 'all_adverse_events.csv')
            if not os.path.exists(ae_file):
                return jsonify({'error': 'Adverse events file not found'}), 400

            ae_df = pd.read_csv(ae_file)

            if filtered_studies:
                ae_df = ae_df[ae_df['study_id'].isin(filtered_studies)]

            search_lower = search_term.lower()
            matching_terms = {}
            ae_columns = ['term', 'preferred_term', 'system_organ_class', 'event_term']

            for col in ae_columns:
                if col in ae_df.columns:
                    mask = ae_df[col].str.contains(search_lower, case=False, na=False)
                    matching_rows = ae_df[mask]

                    for _, row in matching_rows.iterrows():
                        exact_term = str(row[col]).strip()
                        study_id = row['study_id']
                        exact_term_lower = exact_term.lower()

                        # SKIP duplicate search terms
                        if exact_term_lower == search_lower:
                            continue

                        if exact_term_lower not in matching_terms:
                            matching_terms[exact_term_lower] = {'display_term': exact_term, 'studies': set()}
                        matching_terms[exact_term_lower]['studies'].add(study_id)

            related_terms = []
            total_studies = set()

            for term_lower, term_data in matching_terms.items():
                study_ids = term_data['studies']
                display_term = term_data['display_term']
                study_count = len(study_ids)
                study_word = "study" if study_count == 1 else "studies"
                term_with_count = f"{display_term} ({study_count} {study_word})"
                related_terms.append(term_with_count)
                total_studies.update(study_ids)

            return jsonify({
                'success': True,
                'results': {
                    'search_term': search_term,
                    'studies_found': len(total_studies),
                    'related_terms': related_terms
                }
            })

        else:
            # SIMPLE EXACT SEARCH for everything else (conditions, interventions, etc.)
            from enhanced_filter_search import EnhancedFilterSearch

            search = EnhancedFilterSearch(studies_dir)

            if category == 'conditions':
                results = search.search_conditions_exact(search_term, filtered_studies)
            elif category == 'interventions_names':
                results = search.search_interventions_exact(search_term, filtered_studies)
            else:
                results = search.search_study_characteristics(category, search_term, filtered_studies)

            related_terms = []
            total_studies = set()

            for result in results:
                study_count = result['study_count']
                study_word = "study" if study_count == 1 else "studies"
                term_with_count = f"{result['term']} ({study_count} {study_word})"
                related_terms.append(term_with_count)
                total_studies.update(result['study_ids'])

            return jsonify({
                'success': True,
                'results': {
                    'search_term': search_term,
                    'studies_found': len(total_studies),
                    'related_terms': related_terms
                }
            })

    except Exception as e:
        print(f"❌ Error in discover terms: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/enhanced-filter-studies', methods=['POST'])
def enhanced_filter_studies():
    """Simple direct filtering - get NCT studies for selected terms and find intersection"""
    try:
        data = request.get_json()
        studies_dir = data.get('studies_dir', '')
        criteria_list = data.get('criteria_list', [])

        if not studies_dir or not criteria_list:
            return jsonify({'error': 'Studies directory and criteria list required'}), 400

        # Load adverse events data directly
        import pandas as pd
        import os
        import re

        ae_file = os.path.join(studies_dir, 'all_adverse_events.csv')
        if not os.path.exists(ae_file):
            return jsonify({'error': 'Adverse events file not found'}), 400

        ae_df = pd.read_csv(ae_file)

        # Progressive filtering: start with all studies, then filter step by step
        all_matching_studies = None
        filter_steps = []

        for criteria in criteria_list:
            section = criteria.get('section', '')
            selected_terms = criteria.get('terms', [])
            criteria_type = criteria.get('type', 'additional')

            if not section or not selected_terms:
                continue

            # For adverse events, find NCT studies for each selected term and COMBINE them
            if section == 'adverse_events':
                criteria_matching_studies = set()
                term_details = []  # Store details for each term

                for term in selected_terms:
                    # Remove study count from term
                    clean_term = re.sub(r' \(\d+ stud(?:y|ies)\)$', '', term)

                    # Search in ALL studies (not filtered) to get all studies for this term
                    search_df = ae_df
                    if all_matching_studies is not None:
                        search_df = ae_df[ae_df['study_id'].isin(all_matching_studies)]

                    # Find all rows where any AE column matches this exact term
                    ae_columns = ['term', 'preferred_term', 'system_organ_class', 'event_term']
                    term_studies = set()

                    for col in ae_columns:
                        if col in search_df.columns:
                            # Exact match (case-insensitive)
                            mask = search_df[col].str.lower() == clean_term.lower()
                            matching_rows = search_df[mask]
                            term_studies.update(matching_rows['study_id'].tolist())

                    # Store term details for summary
                    term_details.append({
                        'term': clean_term,
                        'study_count': len(term_studies),
                        'studies': sorted(list(term_studies))
                    })

                    # ADD all studies from this term to the total
                    criteria_matching_studies.update(term_studies)
            else:
                # For non-AE categories, return empty for now (can be extended later)
                criteria_matching_studies = set()

            # Progressive filtering: intersect with previous results
            if all_matching_studies is None:
                # First criteria: use all matching studies
                all_matching_studies = criteria_matching_studies
            else:
                # Subsequent criteria: intersect with previous results
                all_matching_studies = all_matching_studies.intersection(criteria_matching_studies)

            # Record this filtering step
            step_description = f"{criteria_type.title()}: {section.replace('_', ' ').title()}"
            terms_display = ', '.join([re.sub(r' \(\d+ stud(?:y|ies)\)$', '', term) for term in selected_terms])

            step_data = {
                'step': len(filter_steps) + 1,
                'description': step_description,
                'terms': terms_display,
                'studies_remaining': len(all_matching_studies)
            }

            # Add term details for adverse events
            if section == 'adverse_events' and 'term_details' in locals():
                step_data['term_details'] = term_details

            filter_steps.append(step_data)

        # Convert final result to list and sort
        final_studies = sorted(list(all_matching_studies)) if all_matching_studies else []

        return jsonify({
            'success': True,
            'results': {
                'filtered_studies': final_studies,
                'filter_steps': filter_steps
            }
        })

    except Exception as e:
        print(f"❌ Error in filter studies: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/filter-studies', methods=['POST'])
def filter_studies():
    """Apply progressive filtering to studies"""
    try:
        data = request.get_json()
        studies_dir = data.get('studies_dir', 'studies')
        criteria_list = data.get('criteria_list', [])
        use_ai_analysis = data.get('use_ai_analysis', False)
        llm_model = data.get('llm_model', '')

        if not criteria_list:
            return jsonify({'error': 'Criteria list required'}), 400

        if use_ai_analysis and not llm_model:
            return jsonify({'error': 'LLM model required for AI analysis'}), 400

        if not filter_studies_by_criteria:
            return jsonify({'error': 'Analysis functions not available'}), 500

        # Apply progressive filtering (with or without AI)
        if use_ai_analysis:
            print(f"🤖 Using AI analysis with {llm_model}")
            results = filter_studies_by_criteria_ai(studies_dir, criteria_list, llm_model)
        else:
            print(f"🔍 Using fast term matching")
            results = filter_studies_by_criteria(studies_dir, criteria_list)

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['POST'])
def analyze():
    """Handle analysis request"""
    global progress_data

    data = request.json
    criteria = data.get('search_criteria', '').strip()
    llm = data.get('llm', '')

    print(f"🚀 Starting analysis: criteria={criteria}, llm={llm}")

    if not criteria or not llm:
        return jsonify({'error': 'Search criteria and LLM required'}), 400

    # Check if a drug has been loaded
    if 'drug_name' not in progress_data or not progress_data['drug_name']:
        return jsonify({'error': 'No drug selected. Please load studies first.'}), 400

    drug_name = progress_data['drug_name']
    studies_dir = os.path.join('output', drug_name)

    if not os.path.exists(studies_dir) or not os.listdir(studies_dir):
        return jsonify({'error': f'No studies found for {drug_name}. Please load studies first.'}), 400

    # Reset progress for analysis
    progress_data = {
        'status': 'running',
        'operation': 'analyze',
        'progress': 0,
        'message': f'Starting AI analysis...',
        'terminal_output': progress_data.get('terminal_output', []) + [f'🤖 AI Analysis Starting'],
        'results': [],
        'study_count': progress_data.get('study_count', 0)
    }

    # Start analysis in background
    thread = threading.Thread(target=run_analysis, args=(criteria, llm, studies_dir))
    thread.daemon = True
    thread.start()

    return jsonify({'status': 'success', 'message': 'Analysis started'})

@app.route('/api/progress')
def get_progress():
    """Get current progress"""
    return jsonify(progress_data)

@app.route('/api/stop', methods=['POST'])
def stop_extraction():
    """Stop the current extraction process"""
    global current_process, progress_data

    if current_process and current_process.poll() is None:
        try:
            current_process.terminate()
            current_process.wait(timeout=5)
            add_terminal_output("🛑 Extraction stopped by user")
            progress_data.update({
                'status': 'stopped',
                'message': 'Extraction stopped by user'
            })
            return jsonify({'status': 'success', 'message': 'Extraction stopped'})
        except Exception as e:
            return jsonify({'status': 'error', 'message': f'Failed to stop: {str(e)}'})
    else:
        return jsonify({'status': 'info', 'message': 'No active extraction to stop'})

def add_terminal_output(message):
    """Add message to terminal output"""
    global progress_data
    progress_data['terminal_output'].append(message)
    print(f"Terminal: {message}")

def run_extraction(drug):
    """Run the data extraction only"""
    global progress_data, current_process

    try:
        add_terminal_output(f"Drug: {drug}")
        add_terminal_output("=" * 50)

        # Search for studies
        progress_data.update({
            'operation': 'extract',
            'progress': 10,
            'message': f'Searching for {drug} studies...'
        })
        add_terminal_output(f"🔍 Searching for {drug} studies...")

        # Run search script
        result = subprocess.run([sys.executable, 'find_studies.py', drug],
                              capture_output=True, text=True, timeout=120)

        if result.returncode != 0:
            raise Exception(f"Search failed: {result.stderr}")

        add_terminal_output("✅ Search completed successfully")

        # Extract studies
        progress_data.update({
            'operation': 'extract',
            'progress': 50,
            'message': 'Running batch extraction...'
        })
        add_terminal_output("📁 Starting batch extraction...")

        # Create output directory structure
        output_dir = 'output'
        drug_dir = os.path.join(output_dir, drug)
        os.makedirs(drug_dir, exist_ok=True)

        # Run automated batch extraction with drug-specific directory
        print(f"🐍 Using Python interpreter: {sys.executable}")
        print(f"📂 Extracting to directory: {drug_dir}")
        print(f"🔍 Running command: {sys.executable} -u retrieve_studies.py {drug} {drug_dir}")

        # Use the full path to the Python interpreter
        python_path = sys.executable

        # Run the extraction script with detailed output
        current_process = subprocess.Popen([python_path, '-u', 'retrieve_studies.py', drug, drug_dir],
                                         stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                         text=True, bufsize=0, universal_newlines=True)
        process = current_process

        # Stream output in real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                add_terminal_output(line)

                # Update progress based on output patterns
                if "[" in line and "/" in line and "]" in line:
                    try:
                        # Extract progress from "[X/Y]" pattern
                        start = line.find("[") + 1
                        end = line.find("]")
                        progress_part = line[start:end]
                        if "/" in progress_part:
                            current, total = map(int, progress_part.split("/"))
                            progress_pct = 50 + (current / total * 40)  # 50-90%
                            progress_data['progress'] = min(90, progress_pct)
                            progress_data['message'] = f'Processing study {current}/{total}...'
                    except:
                        pass

                # Check for completion markers
                if "BATCH EXTRACTION COMPLETED" in line:
                    progress_data['progress'] = 100
                    progress_data['message'] = 'Extraction completed!'

        process.wait()

        if process.returncode != 0:
            raise Exception("Batch extraction failed")

        # Count extracted studies
        drug_dir = os.path.join('output', drug)
        study_count = len([d for d in os.listdir(drug_dir) if os.path.isdir(os.path.join(drug_dir, d))])

        progress_data.update({
            'status': 'completed',
            'operation': 'extract',
            'progress': 100,
            'message': f'Extraction completed! {study_count} studies extracted',
            'study_count': study_count,
            'drug_name': drug
        })
        add_terminal_output("✅ Batch extraction completed!")
        add_terminal_output(f"📊 Total studies extracted: {study_count}")
        add_terminal_output(f"📁 Data saved to: output/{drug}/")
        add_terminal_output("Ready for AI analysis...")

    except Exception as e:
        error_msg = f"Error: {str(e)}"
        add_terminal_output(f"❌ {error_msg}")
        progress_data.update({
            'status': 'error',
            'message': error_msg
        })

def run_analysis(criteria, llm, studies_dir):
    """Run the AI analysis only"""
    global progress_data, current_process

    try:
        add_terminal_output(f"Search Criteria: {criteria}")
        add_terminal_output(f"AI Model: {llm}")
        add_terminal_output("=" * 50)

        # AI Analysis
        progress_data.update({
            'operation': 'analyze',
            'progress': 0,
            'message': f'Starting AI analysis with {llm}...'
        })
        add_terminal_output(f"🤖 Starting AI analysis with {llm}...")

        # Run AI analysis with unbuffered output, specifying the drug-specific directory
        current_process = subprocess.Popen([sys.executable, '-u', 'meta_analysis.py', criteria, llm, '--studies-dir', studies_dir],
                                         stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                         text=True, bufsize=0, universal_newlines=True)
        process = current_process

        # Stream AI analysis output in real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                add_terminal_output(line)

                # Update progress based on AI analysis patterns
                if "[" in line and "/" in line and "]" in line and "Analyzing" in line:
                    try:
                        start = line.find("[") + 1
                        end = line.find("]")
                        progress_part = line[start:end]
                        if "/" in progress_part:
                            current, total = map(int, progress_part.split("/"))
                            progress_pct = (current / total * 90)  # 0-90%
                            progress_data['progress'] = min(90, progress_pct)
                            progress_data['message'] = f'Analyzing study {current}/{total}...'
                    except:
                        pass

                # Check for AI completion markers
                if "AI ANALYSIS SUMMARY" in line or "ANALYSIS COMPLETED" in line:
                    progress_data['progress'] = 100
                    progress_data['message'] = 'AI analysis completed!'

        process.wait()

        if process.returncode != 0:
            raise Exception("AI analysis failed")

        # Load final results with drug-specific filename
        try:
            # Extract drug name from studies_dir (e.g., "output/canagliflozin" -> "canagliflozin")
            drug_name = os.path.basename(studies_dir)

            # Try drug-specific filename first, then fallback to old name
            results_filename = f"output/{drug_name}_ai_analysis_results.json"
            try:
                with open(results_filename, 'r') as f:
                    analysis_results = json.load(f)
            except FileNotFoundError:
                # Fallback to old filename
                with open('ai_analysis_results.json', 'r') as f:
                    analysis_results = json.load(f)

            # Handle both new two-tier structure and old structure
            strict_matches = analysis_results.get('strict_matches', [])
            suggested_matches = analysis_results.get('suggested_matches', [])
            matching_studies = analysis_results.get('matching_studies', strict_matches)  # backward compatibility
            total_analyzed = analysis_results.get('total_analyzed', 0)

            progress_data.update({
                'status': 'completed',
                'operation': 'analyze',
                'progress': 100,
                'message': f'Analysis complete! Found {len(strict_matches)} strict matches and {len(suggested_matches)} suggested matches',
                'strict_matches': strict_matches,
                'suggested_matches': suggested_matches,
                'results': matching_studies  # backward compatibility
            })

            add_terminal_output("=" * 50)
            add_terminal_output("🎉 ANALYSIS COMPLETED!")
            add_terminal_output(f"Total studies analyzed: {total_analyzed}")
            add_terminal_output(f"Strict matches (ALL keywords): {len(strict_matches)}")
            add_terminal_output(f"Suggested matches (related terms): {len(suggested_matches)}")
            add_terminal_output(f"Total relevant studies: {len(strict_matches) + len(suggested_matches)}")

            if strict_matches:
                add_terminal_output("\n✅ STRICT MATCHES:")
                for i, study in enumerate(strict_matches, 1):
                    confidence = study.get('analysis', {}).get('confidence', 0)
                    add_terminal_output(f"{i}. {study['nct_id']} (confidence: {confidence:.2f})")

            if suggested_matches:
                add_terminal_output("\n💡 SUGGESTED MATCHES:")
                for i, study in enumerate(suggested_matches, 1):
                    confidence = study.get('analysis', {}).get('confidence', 0)
                    add_terminal_output(f"{i}. {study['nct_id']} (confidence: {confidence:.2f})")

        except Exception as e:
            raise Exception(f"Failed to load results: {str(e)}")

    except Exception as e:
        error_msg = f"Error: {str(e)}"
        add_terminal_output(f"❌ {error_msg}")
        progress_data.update({
            'status': 'error',
            'message': error_msg
        })

@app.route('/api/prepare-meta-analysis', methods=['POST'])
def prepare_meta_analysis():
    """
    Prepare filtered studies for meta-analysis by copying them to output_meta folder

    Expected JSON payload:
    {
        "drug_folder": "diabetes/canagliflozin",
        "filtered_studies": ["NCT01234567", "NCT01234568", ...]
    }
    """
    try:
        data = request.json
        drug_folder = data.get('drug_folder', '').strip()
        filtered_studies = data.get('filtered_studies', [])

        if not drug_folder:
            return jsonify({'success': False, 'message': 'Drug folder is required'}), 400

        if not filtered_studies:
            return jsonify({'success': False, 'message': 'No filtered studies provided'}), 400

        # Parse project and drug from drug_folder (e.g., "diabetes/canagliflozin")
        if '/' in drug_folder:
            project_name, drug_name = drug_folder.split('/', 1)
        else:
            # Fallback for old format
            project_name = 'default_project'
            drug_name = drug_folder

        # Create output_meta directory structure
        output_meta_base = 'output_meta'
        output_meta_project = os.path.join(output_meta_base, project_name)
        output_meta_drug = os.path.join(output_meta_project, drug_name)

        # Create directories
        os.makedirs(output_meta_drug, exist_ok=True)

        # Source directory
        source_dir = os.path.join('output', drug_folder)

        if not os.path.exists(source_dir):
            return jsonify({'success': False, 'message': f'Source directory not found: {source_dir}'}), 404

        # Copy filtered studies
        studies_copied = 0
        for study_id in filtered_studies:
            source_study_dir = os.path.join(source_dir, study_id)
            target_study_dir = os.path.join(output_meta_drug, study_id)

            if os.path.exists(source_study_dir):
                # Copy entire study directory
                if os.path.exists(target_study_dir):
                    import shutil
                    shutil.rmtree(target_study_dir)

                import shutil
                shutil.copytree(source_study_dir, target_study_dir)
                studies_copied += 1
                print(f"📋 Copied study {study_id} to meta-analysis workspace")

        if studies_copied == 0:
            return jsonify({'success': False, 'message': 'No studies were copied - check if study directories exist'}), 400

        print(f"✅ Meta-analysis preparation complete: {studies_copied} studies copied to {output_meta_drug}")

        return jsonify({
            'success': True,
            'studies_copied': studies_copied,
            'output_path': output_meta_drug,
            'project_name': project_name,
            'drug_name': drug_name,
            'message': f'Successfully prepared {studies_copied} studies for meta-analysis'
        })

    except Exception as e:
        print(f"❌ Error preparing meta-analysis: {e}")
        return create_error_response(str(e))

@app.route('/api/run-meta-analysis', methods=['POST'])
def run_meta_analysis():
    """
    Run R meta-analysis script to generate PRISMA diagram and other analyses

    Expected JSON payload:
    {
        "project_name": "diabetes",
        "drug_name": "canagliflozin"
    }
    """
    try:
        data = request.json
        project_name = data.get('project_name', '').strip()
        drug_name = data.get('drug_name', '').strip()

        if not project_name or not drug_name:
            return jsonify({'success': False, 'message': 'Project name and drug name are required'}), 400

        # Check if R script exists
        r_script_path = 'meta_analysis_R.R'
        if not os.path.exists(r_script_path):
            return jsonify({'success': False, 'message': 'R script not found'}), 404

        # Create R output directory
        r_output_dir = os.path.join('output_meta', project_name, 'R_output')
        os.makedirs(r_output_dir, exist_ok=True)

        # Run R script
        import subprocess
        try:
            result = subprocess.run([
                'Rscript', r_script_path, project_name, drug_name
            ], capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                # Check if PRISMA diagram was created
                prisma_path = os.path.join(r_output_dir, 'prisma_flow_diagram.png')
                if os.path.exists(prisma_path):
                    print(f"✅ R meta-analysis completed: PRISMA diagram created at {prisma_path}")
                    return jsonify({
                        'success': True,
                        'message': 'Meta-analysis completed successfully',
                        'output_path': prisma_path,
                        'r_output': result.stdout
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': 'R script ran but PRISMA diagram not found',
                        'r_output': result.stdout,
                        'r_error': result.stderr
                    })
            else:
                return jsonify({
                    'success': False,
                    'message': f'R script failed with return code {result.returncode}',
                    'r_error': result.stderr,
                    'r_output': result.stdout
                })

        except subprocess.TimeoutExpired:
            return jsonify({'success': False, 'message': 'R script execution timed out'}), 408
        except FileNotFoundError:
            return jsonify({'success': False, 'message': 'R/Rscript not found. Please install R.'}), 500

    except Exception as e:
        print(f"❌ Error running meta-analysis: {e}")
        return create_error_response(str(e))

@app.route('/api/generate-table1', methods=['POST'])
def generate_table1():
    """
    Generate Table 1: Characteristics of Included Studies

    Expected JSON payload:
    {
        "project_name": "diabetes",
        "drug_name": "canagliflozin",
        "ai_model": "llama2" (optional)
    }
    """
    try:
        data = request.json
        project_name = data.get('project_name', '').strip()
        drug_name = data.get('drug_name', '').strip()
        ai_model = data.get('ai_model', '').strip()

        if not project_name or not drug_name:
            return jsonify({'success': False, 'message': 'Project name and drug name are required'}), 400

        # Check if studies directory exists
        studies_dir = os.path.join('output_meta', project_name, drug_name)
        if not os.path.exists(studies_dir):
            return jsonify({'success': False, 'message': f'Studies directory not found: {studies_dir}'}), 404

        # Check if meta_analysis.py exists
        meta_analysis_script = 'meta_analysis.py'
        if not os.path.exists(meta_analysis_script):
            return jsonify({'success': False, 'message': 'Meta-analysis script not found'}), 404

        # Run Python script to generate Table 1
        import subprocess
        try:
            cmd = ['python', meta_analysis_script, 'dummy_criteria', ai_model or 'dummy_model',
                   '--studies-dir', studies_dir, '--generate-table1']

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

            if result.returncode == 0:
                # Check if CSV was created (use the correct filename from meta_analysis_table1.py)
                csv_path = os.path.join('output_meta', project_name, 'R_output', 'table1_characteristics_of_included_studies.csv')
                if os.path.exists(csv_path):
                    print(f"✅ Table 1 generated: {csv_path}")
                    return jsonify({
                        'success': True,
                        'message': 'Table 1 generated successfully',
                        'csv_path': csv_path,
                        'output': result.stdout
                    })
                else:
                    # Also check for other possible CSV files that might have been created
                    alt_paths = [
                        os.path.join('output_meta', project_name, 'R_output', 'table1_raw_variables.csv'),
                        os.path.join('output_meta', project_name, 'R_output', 'table1_meta_variables.csv')
                    ]

                    existing_files = [path for path in alt_paths if os.path.exists(path)]

                    if existing_files:
                        print(f"✅ Table 1 files generated: {existing_files}")
                        return jsonify({
                            'success': True,
                            'message': 'Table 1 generated successfully',
                            'csv_path': existing_files[0],  # Return first found file
                            'all_files': existing_files,
                            'output': result.stdout
                        })
                    else:
                        return jsonify({
                            'success': False,
                            'message': 'Script ran but CSV file not found',
                            'expected_path': csv_path,
                            'output': result.stdout,
                            'error': result.stderr
                        })
            else:
                return jsonify({
                    'success': False,
                    'message': f'Script failed with return code {result.returncode}',
                    'error': result.stderr,
                    'output': result.stdout
                })

        except subprocess.TimeoutExpired:
            return jsonify({'success': False, 'message': 'Table 1 generation timed out'}), 408
        except FileNotFoundError:
            return jsonify({'success': False, 'message': 'Python not found in PATH'}), 500

    except Exception as e:
        print(f"❌ Error generating Table 1: {e}")
        return create_error_response(str(e))

if __name__ == '__main__':
    import sys

    # Allow port to be specified as command line argument
    port = 9090  # Default port
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number, using default 9090")

    print("🌐 Starting Clinical Trials Server")
    print(f"📍 Open: http://localhost:{port}")

    # Check Ollama
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=3)
        if response.status_code == 200:
            print("✅ Ollama is running")
        else:
            print("⚠️ Ollama not responding")
    except:
        print("❌ Ollama not running")

    import logging
    log = logging.getLogger('werkzeug')
    log.setLevel(logging.ERROR)
    app.run(debug=False, host='0.0.0.0', port=port)
