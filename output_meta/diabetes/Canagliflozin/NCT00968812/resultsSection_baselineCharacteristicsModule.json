{"groups": [{"id": "BG000", "title": "Canagliflozin 100 mg", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily with protocol-specified doses of metformin for 104 weeks."}, {"id": "BG001", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily with protocol-specified doses of metformin for 104 weeks."}, {"id": "BG002", "title": "Glimepiride", "description": "Each patient received glimepiride, at protocol-specified doses, once daily in combination with protocol-specified doses of metformin for 104 weeks."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "483"}, {"groupId": "BG001", "value": "485"}, {"groupId": "BG002", "value": "482"}, {"groupId": "BG003", "value": "1450"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "397"}, {"groupId": "BG001", "value": "411"}, {"groupId": "BG002", "value": "399"}, {"groupId": "BG003", "value": "1207"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "86"}, {"groupId": "BG001", "value": "74"}, {"groupId": "BG002", "value": "83"}, {"groupId": "BG003", "value": "243"}]}]}]}, {"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "56.4", "spread": "9.49"}, {"groupId": "BG001", "value": "55.8", "spread": "9.17"}, {"groupId": "BG002", "value": "56.3", "spread": "9.01"}, {"groupId": "BG003", "value": "56.2", "spread": "9.22"}]}]}]}, {"title": "Gender", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "231"}, {"groupId": "BG001", "value": "244"}, {"groupId": "BG002", "value": "219"}, {"groupId": "BG003", "value": "694"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "252"}, {"groupId": "BG001", "value": "241"}, {"groupId": "BG002", "value": "263"}, {"groupId": "BG003", "value": "756"}]}]}]}, {"title": "Region Enroll", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "ARGENTINA", "categories": [{"measurements": [{"groupId": "BG000", "value": "18"}, {"groupId": "BG001", "value": "18"}, {"groupId": "BG002", "value": "18"}, {"groupId": "BG003", "value": "54"}]}]}, {"title": "BULGARIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "7"}, {"groupId": "BG002", "value": "7"}, {"groupId": "BG003", "value": "21"}]}]}, {"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "19"}, {"groupId": "BG001", "value": "20"}, {"groupId": "BG002", "value": "19"}, {"groupId": "BG003", "value": "58"}]}]}, {"title": "COSTA RICA", "categories": [{"measurements": [{"groupId": "BG000", "value": "10"}, {"groupId": "BG001", "value": "9"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "28"}]}]}, {"title": "DENMARK", "categories": [{"measurements": [{"groupId": "BG000", "value": "24"}, {"groupId": "BG001", "value": "25"}, {"groupId": "BG002", "value": "25"}, {"groupId": "BG003", "value": "74"}]}]}, {"title": "FINLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "18"}, {"groupId": "BG001", "value": "17"}, {"groupId": "BG002", "value": "19"}, {"groupId": "BG003", "value": "54"}]}]}, {"title": "GERMANY", "categories": [{"measurements": [{"groupId": "BG000", "value": "6"}, {"groupId": "BG001", "value": "7"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "19"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "55"}, {"groupId": "BG001", "value": "55"}, {"groupId": "BG002", "value": "56"}, {"groupId": "BG003", "value": "166"}]}]}, {"title": "ISRAEL", "categories": [{"measurements": [{"groupId": "BG000", "value": "14"}, {"groupId": "BG001", "value": "15"}, {"groupId": "BG002", "value": "14"}, {"groupId": "BG003", "value": "43"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "24"}, {"groupId": "BG001", "value": "25"}, {"groupId": "BG002", "value": "24"}, {"groupId": "BG003", "value": "73"}]}]}, {"title": "NORWAY", "categories": [{"measurements": [{"groupId": "BG000", "value": "9"}, {"groupId": "BG001", "value": "9"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "27"}]}]}, {"title": "PHILIPPINES", "categories": [{"measurements": [{"groupId": "BG000", "value": "14"}, {"groupId": "BG001", "value": "13"}, {"groupId": "BG002", "value": "13"}, {"groupId": "BG003", "value": "40"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "14"}, {"groupId": "BG001", "value": "15"}, {"groupId": "BG002", "value": "15"}, {"groupId": "BG003", "value": "44"}]}]}, {"title": "ROMANIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "43"}, {"groupId": "BG001", "value": "43"}, {"groupId": "BG002", "value": "44"}, {"groupId": "BG003", "value": "130"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "23"}, {"groupId": "BG001", "value": "22"}, {"groupId": "BG002", "value": "22"}, {"groupId": "BG003", "value": "67"}]}]}, {"title": "SLOVAKIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "15"}, {"groupId": "BG001", "value": "14"}, {"groupId": "BG002", "value": "13"}, {"groupId": "BG003", "value": "42"}]}]}, {"title": "SOUTH KOREA", "categories": [{"measurements": [{"groupId": "BG000", "value": "31"}, {"groupId": "BG001", "value": "32"}, {"groupId": "BG002", "value": "31"}, {"groupId": "BG003", "value": "94"}]}]}, {"title": "UKRAINE", "categories": [{"measurements": [{"groupId": "BG000", "value": "22"}, {"groupId": "BG001", "value": "22"}, {"groupId": "BG002", "value": "22"}, {"groupId": "BG003", "value": "66"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "117"}, {"groupId": "BG001", "value": "117"}, {"groupId": "BG002", "value": "116"}, {"groupId": "BG003", "value": "350"}]}]}]}]}