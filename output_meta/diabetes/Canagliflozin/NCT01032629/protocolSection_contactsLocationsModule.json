{"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Birmingham", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 33.52066, "lon": -86.80249}}, {"city": "Mesa", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.42227, "lon": -111.82264}}, {"city": "Tucson", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 32.22174, "lon": -110.92648}}, {"city": "La Mesa", "state": "California", "country": "United States", "geoPoint": {"lat": 32.76783, "lon": -117.02308}}, {"city": "Los Gatos", "state": "California", "country": "United States", "geoPoint": {"lat": 37.22661, "lon": -121.97468}}, {"city": "Pismo Beach", "state": "California", "country": "United States", "geoPoint": {"lat": 35.14275, "lon": -120.64128}}, {"city": "Stockton", "state": "California", "country": "United States", "geoPoint": {"lat": 37.9577, "lon": -121.29078}}, {"city": "Walnut Creek", "state": "California", "country": "United States", "geoPoint": {"lat": 37.90631, "lon": -122.06496}}, {"city": "<PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.81954, "lon": -80.35533}}, {"city": "Fort Lauderdale", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.12231, "lon": -80.14338}}, {"city": "Jacksonville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.33218, "lon": -81.65565}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "Plantation", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.13421, "lon": -80.23184}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "Duluth", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 34.00288, "lon": -84.14464}}, {"city": "Boise", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.6135, "lon": -116.20345}}, {"city": "Eagle", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.69544, "lon": -116.35401}}, {"city": "Meridian", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.61211, "lon": -116.39151}}, {"city": "Peoria", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 40.69365, "lon": -89.58899}}, {"city": "Topeka", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 39.04833, "lon": -95.67804}}, {"city": "Crestview Hills", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 39.02728, "lon": -84.58494}}, {"city": "Lexington", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 37.98869, "lon": -84.47772}}, {"city": "Auburn", "state": "Maine", "country": "United States", "geoPoint": {"lat": 44.09785, "lon": -70.23117}}, {"city": "Scarborough", "state": "Maine", "country": "United States", "geoPoint": {"lat": 43.57814, "lon": -70.32172}}, {"city": "Oxon Hill", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 38.80345, "lon": -76.9897}}, {"city": "Royal Oak", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.48948, "lon": -83.14465}}, {"city": "Kansas City", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 39.09973, "lon": -94.57857}}, {"city": "Saint Louis", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.62727, "lon": -90.19789}}, {"city": "Omaha", "state": "Nebraska", "country": "United States", "geoPoint": {"lat": 41.25626, "lon": -95.94043}}, {"city": "Camden", "state": "New Jersey", "country": "United States", "geoPoint": {"lat": 39.92595, "lon": -75.11962}}, {"city": "Bronx", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.84985, "lon": -73.86641}}, {"city": "Flushing", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.76538, "lon": -73.81736}}, {"city": "Chapel Hill", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.9132, "lon": -79.05584}}, {"city": "Charlotte", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.22709, "lon": -80.84313}}, {"city": "Cincinnati", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.12713, "lon": -84.51435}}, {"city": "Cleveland", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.4995, "lon": -81.69541}}, {"city": "Columbus", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.96118, "lon": -82.99879}}, {"city": "Gallipolis", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 38.8098, "lon": -82.20237}}, {"city": "Mentor", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.66616, "lon": -81.33955}}, {"city": "Toledo", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.66394, "lon": -83.55521}}, {"city": "Beaver", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.69534, "lon": -80.30478}}, {"city": "Norristown", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.1215, "lon": -75.3399}}, {"city": "<PERSON><PERSON>", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 41.97896, "lon": -76.5155}}, {"city": "Greenville", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.85262, "lon": -82.39401}}, {"city": "Kingsport", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.54843, "lon": -82.56182}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "Odessa", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.84568, "lon": -102.36764}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "Temple", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.09823, "lon": -97.34278}}, {"city": "Draper", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.52467, "lon": -111.86382}}, {"city": "Salt Lake City", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.76078, "lon": -111.89105}}, {"city": "<PERSON>", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.59161, "lon": -111.8841}}, {"city": "West Jordan", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.60967, "lon": -111.9391}}, {"city": "Danville", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.58597, "lon": -79.39502}}, {"city": "Richmond", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 37.55376, "lon": -77.46026}}, {"city": "Spokane", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.65966, "lon": -117.42908}}, {"city": "Tacoma", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.25288, "lon": -122.44429}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Capital Federal", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Ciudad Autonma Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Ciudad Autonoma De Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Cordoba", "country": "Argentina", "geoPoint": {"lat": -31.4135, "lon": -64.18105}}, {"city": "Corrientes", "country": "Argentina", "geoPoint": {"lat": -27.4806, "lon": -58.8341}}, {"city": "<PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -32.92863, "lon": -68.8351}}, {"city": "Mar Del Plata", "country": "Argentina", "geoPoint": {"lat": -38.00228, "lon": -57.55754}}, {"city": "Rosario", "country": "Argentina", "geoPoint": {"lat": -32.94682, "lon": -60.63932}}, {"city": "Santa Fe", "country": "Argentina", "geoPoint": {"lat": -31.63333, "lon": -60.7}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Australia", "geoPoint": {"lat": -27.47443, "lon": 152.99213}}, {"city": "Box Hill", "country": "Australia", "geoPoint": {"lat": -37.81887, "lon": 145.12545}}, {"city": "Caboolture", "country": "Australia", "geoPoint": {"lat": -27.08465, "lon": 152.9511}}, {"city": "Caringbah", "country": "Australia", "geoPoint": {"lat": -34.04726, "lon": 151.12051}}, {"city": "<PERSON>", "country": "Australia", "geoPoint": {"lat": -37.91667, "lon": 145.11667}}, {"city": "Daw Park", "country": "Australia", "geoPoint": {"lat": -34.98975, "lon": 138.58407}}, {"city": "East Ringwood", "country": "Australia"}, {"city": "<PERSON>", "country": "Australia", "geoPoint": {"lat": -34.74857, "lon": 138.66819}}, {"city": "Hornsby, Nsw 2077", "country": "Australia"}, {"city": "Kippa Ring", "country": "Australia", "geoPoint": {"lat": -27.22586, "lon": 153.0835}}, {"city": "Launceston", "country": "Australia", "geoPoint": {"lat": -41.43876, "lon": 147.13467}}, {"city": "Liverpool", "country": "Australia", "geoPoint": {"lat": -33.90011, "lon": 150.93328}}, {"city": "Melbourne", "country": "Australia", "geoPoint": {"lat": -37.814, "lon": 144.96332}}, {"city": "<PERSON>", "country": "Australia", "geoPoint": {"lat": -35.31644, "lon": 150.4361}}, {"city": "Parkville", "country": "Australia", "geoPoint": {"lat": -37.78333, "lon": 144.95}}, {"city": "Reservoir", "country": "Australia", "geoPoint": {"lat": -37.71667, "lon": 145.0}}, {"city": "<PERSON>", "country": "Australia", "geoPoint": {"lat": -31.06485, "lon": 152.72879}}, {"city": "Southport", "country": "Australia", "geoPoint": {"lat": -27.96724, "lon": 153.39796}}, {"city": "St Leonards", "country": "Australia", "geoPoint": {"lat": -33.82344, "lon": 151.19836}}, {"city": "West Heidelberg", "country": "Australia"}, {"city": "Wollongong", "country": "Australia", "geoPoint": {"lat": -34.424, "lon": 150.89345}}, {"city": "Woolloongabba", "country": "Australia", "geoPoint": {"lat": -27.48855, "lon": 153.03655}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Belgium", "geoPoint": {"lat": 49.68333, "lon": 5.81667}}, {"city": "Bonheiden", "country": "Belgium", "geoPoint": {"lat": 51.02261, "lon": 4.54714}}, {"city": "Brussel", "country": "Belgium", "geoPoint": {"lat": 50.85045, "lon": 4.34878}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Belgium", "geoPoint": {"lat": 51.15662, "lon": 4.44504}}, {"city": "Genk", "country": "Belgium", "geoPoint": {"lat": 50.965, "lon": 5.50082}}, {"city": "Leuven", "country": "Belgium", "geoPoint": {"lat": 50.87959, "lon": 4.70093}}, {"city": "Calgary", "state": "Alberta", "country": "Canada", "geoPoint": {"lat": 51.05011, "lon": -114.08529}}, {"city": "Coquitlam", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.28297, "lon": -122.75262}}, {"city": "Winnipeg", "state": "Manitoba", "country": "Canada", "geoPoint": {"lat": 49.8844, "lon": -97.14704}}, {"city": "St. John'S", "state": "Newfoundland and Labrador", "country": "Canada", "geoPoint": {"lat": 47.56494, "lon": -52.70931}}, {"city": "Brampton", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.68341, "lon": -79.76633}}, {"city": "London", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.98339, "lon": -81.23304}}, {"city": "Mississauga", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.5789, "lon": -79.6583}}, {"city": "Nemarket", "state": "Ontario", "country": "Canada"}, {"city": "Sarnia", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.97866, "lon": -82.40407}}, {"city": "Sudbury", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 46.49, "lon": -80.99001}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "<PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.56995, "lon": -73.692}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.80326, "lon": -71.17793}}, {"city": "Montreal", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.50884, "lon": -73.58781}}, {"city": "Val-Belair", "state": "Quebec", "country": "Canada"}, {"city": "Saskatoon", "state": "Saskatchewan", "country": "Canada", "geoPoint": {"lat": 52.13238, "lon": -106.66892}}, {"city": "Bogota", "country": "Colombia", "geoPoint": {"lat": 4.60971, "lon": -74.08175}}, {"city": "Floridablanca", "country": "Colombia", "geoPoint": {"lat": 7.06222, "lon": -73.08644}}, {"city": "Kralupy Nad Vltavou", "country": "Czechia", "geoPoint": {"lat": 50.24107, "lon": 14.31149}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 49.04893, "lon": 16.31169}}, {"city": "Olomouc 9", "country": "Czechia", "geoPoint": {"lat": 49.59552, "lon": 17.25175}}, {"city": "Ostrava", "country": "Czechia", "geoPoint": {"lat": 49.83465, "lon": 18.28204}}, {"city": "Pisek", "country": "Czechia", "geoPoint": {"lat": 49.3088, "lon": 14.1475}}, {"city": "Prague 5", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 11", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 8", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Znojmo N/A", "country": "Czechia"}, {"city": "Pärnu", "country": "Estonia", "geoPoint": {"lat": 58.38588, "lon": 24.49711}}, {"city": "Tallinn", "country": "Estonia", "geoPoint": {"lat": 59.43696, "lon": 24.75353}}, {"city": "Tartu", "country": "Estonia", "geoPoint": {"lat": 58.38062, "lon": 26.72509}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Estonia", "geoPoint": {"lat": 58.36389, "lon": 25.59}}, {"city": "Amiens", "country": "France", "geoPoint": {"lat": 49.9, "lon": 2.3}}, {"city": "Nimes Cedex 9", "country": "France", "geoPoint": {"lat": 43.83333, "lon": 4.35}}, {"city": "<PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 44.81011, "lon": -0.64129}}, {"city": "Berlin", "country": "Germany", "geoPoint": {"lat": 52.52437, "lon": 13.41053}}, {"city": "Dortmund", "country": "Germany", "geoPoint": {"lat": 51.51494, "lon": 7.466}}, {"city": "Dresden", "country": "Germany", "geoPoint": {"lat": 51.05089, "lon": 13.73832}}, {"city": "Hamburg", "country": "Germany", "geoPoint": {"lat": 53.57532, "lon": 10.01534}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 48.66667, "lon": 13.08333}}, {"city": "Mainz", "country": "Germany", "geoPoint": {"lat": 49.98419, "lon": 8.2791}}, {"city": "Münster", "country": "Germany", "geoPoint": {"lat": 51.96236, "lon": 7.62571}}, {"city": "Pirna", "country": "Germany", "geoPoint": {"lat": 50.95843, "lon": 13.93702}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 49.31366, "lon": 6.75154}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 49.32083, "lon": 8.43111}}, {"city": "Villingen-Schwenningen", "country": "Germany", "geoPoint": {"lat": 48.06226, "lon": 8.49358}}, {"city": "Budapest", "country": "Hungary", "geoPoint": {"lat": 47.49801, "lon": 19.03991}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 46.90618, "lon": 19.69128}}, {"city": "Mosonmagyar<PERSON>v<PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 47.86789, "lon": 17.26994}}, {"city": "Zalaegerszeg", "country": "Hungary", "geoPoint": {"lat": 46.84, "lon": 16.84389}}, {"city": "Ahemadabad", "country": "India"}, {"city": "Ahmedabad", "country": "India", "geoPoint": {"lat": 23.02579, "lon": 72.58727}}, {"city": "Ambawadi", "country": "India"}, {"city": "Bangalore, Karnataka", "country": "India"}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Belgaum", "country": "India", "geoPoint": {"lat": 15.85212, "lon": 74.50447}}, {"city": "Calicut", "country": "India", "geoPoint": {"lat": 11.24802, "lon": 75.7804}}, {"city": "Chennai", "country": "India", "geoPoint": {"lat": 13.08784, "lon": 80.27847}}, {"city": "Cochin", "country": "India", "geoPoint": {"lat": 9.93988, "lon": 76.26022}}, {"city": "Coimbatore", "country": "India", "geoPoint": {"lat": 11.00555, "lon": 76.96612}}, {"city": "Ernakulam", "country": "India", "geoPoint": {"lat": 9.96714, "lon": 76.29036}}, {"city": "Ghaziabad", "country": "India", "geoPoint": {"lat": 28.66535, "lon": 77.43915}}, {"city": "Hyderabad", "country": "India", "geoPoint": {"lat": 17.38405, "lon": 78.45636}}, {"city": "Indore", "country": "India", "geoPoint": {"lat": 22.71792, "lon": 75.8333}}, {"city": "Jaipur", "country": "India", "geoPoint": {"lat": 26.91962, "lon": 75.78781}}, {"city": "Karnal", "country": "India", "geoPoint": {"lat": 29.69197, "lon": 76.98448}}, {"city": "Kerala", "country": "India"}, {"city": "<PERSON><PERSON>", "country": "India", "geoPoint": {"lat": 9.93988, "lon": 76.26022}}, {"city": "Kolkata", "country": "India", "geoPoint": {"lat": 22.56263, "lon": 88.36304}}, {"city": "Lucknow", "country": "India", "geoPoint": {"lat": 26.83928, "lon": 80.92313}}, {"city": "Mangalore", "country": "India", "geoPoint": {"lat": 12.91723, "lon": 74.85603}}, {"city": "Mumbai", "country": "India", "geoPoint": {"lat": 19.07283, "lon": 72.88261}}, {"city": "Mysore", "country": "India", "geoPoint": {"lat": 12.29791, "lon": 76.63925}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "New Delhi", "country": "India", "geoPoint": {"lat": 28.63576, "lon": 77.22445}}, {"city": "<PERSON><PERSON>", "country": "India", "geoPoint": {"lat": 25.59408, "lon": 85.13563}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "Rajkot", "country": "India", "geoPoint": {"lat": 22.29161, "lon": 70.79322}}, {"city": "Trivandrum, Kerala", "country": "India"}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "India"}, {"city": "Vijayawada", "country": "India", "geoPoint": {"lat": 16.50745, "lon": 80.6466}}, {"city": "Visakhapatnam", "country": "India", "geoPoint": {"lat": 17.68009, "lon": 83.20161}}, {"city": "Beer Sheba", "country": "Israel", "geoPoint": {"lat": 31.25181, "lon": 34.7913}}, {"city": "<PERSON>lon", "country": "Israel", "geoPoint": {"lat": 32.01034, "lon": 34.77918}}, {"city": "Jerusalem", "country": "Israel", "geoPoint": {"lat": 31.76904, "lon": 35.21633}}, {"city": "Kfar Saba", "country": "Israel", "geoPoint": {"lat": 32.175, "lon": 34.90694}}, {"city": "Luxembourg", "country": "Luxembourg", "geoPoint": {"lat": 49.61167, "lon": 6.13}}, {"city": "Georgetown", "country": "Malaysia"}, {"city": "<PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 1.4655, "lon": 103.7578}}, {"city": "Kota Bharu", "country": "Malaysia", "geoPoint": {"lat": 6.13328, "lon": 102.2386}}, {"city": "Kuala Lumpur N/A", "country": "Malaysia"}, {"city": "Petaling Jaya", "country": "Malaysia", "geoPoint": {"lat": 3.10726, "lon": 101.60671}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 3.55, "lon": 102.56667}}, {"city": "Subang Jaya", "country": "Malaysia", "geoPoint": {"lat": 3.04384, "lon": 101.58062}}, {"city": "Aguascalientes", "country": "Mexico", "geoPoint": {"lat": 21.88234, "lon": -102.28259}}, {"city": "Celaya", "country": "Mexico", "geoPoint": {"lat": 20.52353, "lon": -100.8157}}, {"city": "Ciudad De Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Durango", "country": "Mexico", "geoPoint": {"lat": 24.02032, "lon": -104.65756}}, {"city": "Guadalajara", "country": "Mexico", "geoPoint": {"lat": 20.66682, "lon": -103.39182}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Almelo", "country": "Netherlands", "geoPoint": {"lat": 52.35667, "lon": 6.6625}}, {"city": "Almere", "country": "Netherlands", "geoPoint": {"lat": 52.37535, "lon": 5.25295}}, {"city": "Amsterdam", "country": "Netherlands", "geoPoint": {"lat": 52.37403, "lon": 4.88969}}, {"city": "Arnhem", "country": "Netherlands", "geoPoint": {"lat": 51.98, "lon": 5.91111}}, {"city": "Delft", "country": "Netherlands", "geoPoint": {"lat": 52.00667, "lon": 4.35556}}, {"city": "<PERSON>", "country": "Netherlands", "geoPoint": {"lat": 52.95988, "lon": 4.75933}}, {"city": "Dordrecht", "country": "Netherlands", "geoPoint": {"lat": 51.81, "lon": 4.67361}}, {"city": "Eindhoven", "country": "Netherlands", "geoPoint": {"lat": 51.44083, "lon": 5.47778}}, {"city": "Groningen", "country": "Netherlands", "geoPoint": {"lat": 53.21917, "lon": 6.56667}}, {"city": "Hoorn Nh", "country": "Netherlands"}, {"city": "Rotterdam", "country": "Netherlands", "geoPoint": {"lat": 51.9225, "lon": 4.47917}}, {"city": "Tilburg", "country": "Netherlands", "geoPoint": {"lat": 51.55551, "lon": 5.0913}}, {"city": "Utrecht", "country": "Netherlands", "geoPoint": {"lat": 52.09083, "lon": 5.12222}}, {"city": "Velp Gld", "country": "Netherlands"}, {"city": "Zoetermeer", "country": "Netherlands", "geoPoint": {"lat": 52.0575, "lon": 4.49306}}, {"city": "Zwijndrecht", "country": "Netherlands", "geoPoint": {"lat": 51.8175, "lon": 4.63333}}, {"city": "Auckland", "country": "New Zealand", "geoPoint": {"lat": -36.84853, "lon": 174.76349}}, {"city": "Christchurch", "country": "New Zealand", "geoPoint": {"lat": -43.53333, "lon": 172.63333}}, {"city": "Dunedin", "country": "New Zealand", "geoPoint": {"lat": -45.87416, "lon": 170.50361}}, {"city": "Taurang<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -37.68611, "lon": 176.16667}}, {"city": "Wellington", "country": "New Zealand", "geoPoint": {"lat": -41.28664, "lon": 174.77557}}, {"city": "Alesund", "country": "Norway", "geoPoint": {"lat": 62.47225, "lon": 6.15492}}, {"city": "<PERSON><PERSON>", "country": "Norway", "geoPoint": {"lat": 59.83333, "lon": 10.43721}}, {"city": "Bekkestua", "country": "Norway"}, {"city": "Elverum", "country": "Norway", "geoPoint": {"lat": 60.88191, "lon": 11.56231}}, {"city": "<PERSON><PERSON>", "country": "Norway", "geoPoint": {"lat": 60.7945, "lon": 11.06798}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Norway"}, {"city": "Moss", "country": "Norway", "geoPoint": {"lat": 59.43403, "lon": 10.65771}}, {"city": "Oslo", "country": "Norway", "geoPoint": {"lat": 59.91273, "lon": 10.74609}}, {"city": "Skedsmokorset", "country": "Norway", "geoPoint": {"lat": 60.00459, "lon": 11.03278}}, {"city": "Bialystok", "country": "Poland", "geoPoint": {"lat": 53.13333, "lon": 23.16433}}, {"city": "Ciechocinek", "country": "Poland", "geoPoint": {"lat": 52.87908, "lon": 18.79505}}, {"city": "Gniewkowo", "country": "Poland", "geoPoint": {"lat": 52.89461, "lon": 18.40785}}, {"city": "Grudziadz", "country": "Poland", "geoPoint": {"lat": 53.48411, "lon": 18.75366}}, {"city": "Katowice", "country": "Poland", "geoPoint": {"lat": 50.25841, "lon": 19.02754}}, {"city": "Krakow", "country": "Poland", "geoPoint": {"lat": 50.06143, "lon": 19.93658}}, {"city": "Lublin", "country": "Poland", "geoPoint": {"lat": 51.25, "lon": 22.56667}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 52.54682, "lon": 19.70638}}, {"city": "Poznan", "country": "Poland", "geoPoint": {"lat": 52.40692, "lon": 16.92993}}, {"city": "Rzeszow", "country": "Poland", "geoPoint": {"lat": 50.04132, "lon": 21.99901}}, {"city": "Slawkow", "country": "Poland", "geoPoint": {"lat": 50.29943, "lon": 19.38967}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Warszawa", "country": "Poland", "geoPoint": {"lat": 52.22977, "lon": 21.01178}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Arkhangelsk", "country": "Russian Federation", "geoPoint": {"lat": 64.5401, "lon": 40.5433}}, {"city": "Chelyabinsk", "country": "Russian Federation", "geoPoint": {"lat": 55.15402, "lon": 61.42915}}, {"city": "Ekaterinburg", "country": "Russian Federation", "geoPoint": {"lat": 56.8519, "lon": 60.6122}}, {"city": "Kemerovo", "country": "Russian Federation", "geoPoint": {"lat": 55.33333, "lon": 86.08333}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 58.59665, "lon": 49.66007}}, {"city": "Kursk", "country": "Russian Federation", "geoPoint": {"lat": 51.73733, "lon": 36.18735}}, {"city": "Moscow N/A", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Moscow", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Nizhny Novgorod", "country": "Russian Federation", "geoPoint": {"lat": 56.32867, "lon": 44.00205}}, {"city": "Novosibirsk", "country": "Russian Federation", "geoPoint": {"lat": 55.0415, "lon": 82.9346}}, {"city": "Omsk", "country": "Russian Federation", "geoPoint": {"lat": 54.99244, "lon": 73.36859}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 53.20066, "lon": 45.00464}}, {"city": "Rostov-On-Don", "country": "Russian Federation", "geoPoint": {"lat": 47.23135, "lon": 39.72328}}, {"city": "Russia", "country": "Russian Federation"}, {"city": "R<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 54.6269, "lon": 39.6916}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Saint-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 51.54056, "lon": 46.00861}}, {"city": "Smolensk", "country": "Russian Federation", "geoPoint": {"lat": 54.7818, "lon": 32.0401}}, {"city": "St Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Tomsk", "country": "Russian Federation", "geoPoint": {"lat": 56.49771, "lon": 84.97437}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 54.19609, "lon": 37.61822}}, {"city": "Tyumen", "country": "Russian Federation", "geoPoint": {"lat": 57.15222, "lon": 65.52722}}, {"city": "Yaroslavl Nap", "country": "Russian Federation"}, {"city": "Yaroslavl", "country": "Russian Federation", "geoPoint": {"lat": 57.62987, "lon": 39.87368}}, {"city": "Alicante", "country": "Spain", "geoPoint": {"lat": 38.34517, "lon": -0.48149}}, {"city": "Almeria", "country": "Spain", "geoPoint": {"lat": 36.83814, "lon": -2.45974}}, {"city": "Barcelona", "country": "Spain", "geoPoint": {"lat": 41.38879, "lon": 2.15899}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Spain", "geoPoint": {"lat": 42.26645, "lon": 2.96163}}, {"city": "Madrid", "country": "Spain", "geoPoint": {"lat": 40.4165, "lon": -3.70256}}, {"city": "Málaga", "country": "Spain", "geoPoint": {"lat": 36.72016, "lon": -4.42034}}, {"city": "Oviedo", "country": "Spain", "geoPoint": {"lat": 43.36029, "lon": -5.84476}}, {"city": "<PERSON><PERSON>", "country": "Spain", "geoPoint": {"lat": 41.15612, "lon": 1.10687}}, {"city": "Sa<PERSON>ell", "country": "Spain", "geoPoint": {"lat": 41.54329, "lon": 2.10942}}, {"city": "San Juan De Alicante", "country": "Spain", "geoPoint": {"lat": 38.40148, "lon": -0.43623}}, {"city": "Santiago De Compostela", "country": "Spain", "geoPoint": {"lat": 42.88052, "lon": -8.54569}}, {"city": "Sevilla N/A", "country": "Spain", "geoPoint": {"lat": 37.38283, "lon": -5.97317}}, {"city": "Valencia", "country": "Spain", "geoPoint": {"lat": 39.46975, "lon": -0.37739}}, {"city": "Viladecans", "country": "Spain", "geoPoint": {"lat": 41.31405, "lon": 2.01427}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Sweden", "geoPoint": {"lat": 57.72101, "lon": 12.9401}}, {"city": "Göteborg", "country": "Sweden", "geoPoint": {"lat": 57.70716, "lon": 11.96679}}, {"city": "Helsingborg", "country": "Sweden", "geoPoint": {"lat": 56.04673, "lon": 12.69437}}, {"city": "Lund", "country": "Sweden", "geoPoint": {"lat": 55.70584, "lon": 13.19321}}, {"city": "Malmö", "country": "Sweden", "geoPoint": {"lat": 55.60587, "lon": 13.00073}}, {"city": "Oskarshamn N/A", "country": "Sweden"}, {"city": "Piteå", "country": "Sweden", "geoPoint": {"lat": 65.31717, "lon": 21.47944}}, {"city": "Stockholm", "country": "Sweden", "geoPoint": {"lat": 59.33258, "lon": 18.0649}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Sweden", "geoPoint": {"lat": 58.34784, "lon": 11.9424}}, {"city": "Dnepropetrovsk", "country": "Ukraine", "geoPoint": {"lat": 48.4593, "lon": 35.03864}}, {"city": "Donetsk", "country": "Ukraine", "geoPoint": {"lat": 48.023, "lon": 37.80224}}, {"city": "Kharkov", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Kyiv", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Odessa", "country": "Ukraine", "geoPoint": {"lat": 46.47747, "lon": 30.73262}}, {"city": "Ternopol", "country": "Ukraine"}, {"city": "Uzhgorod", "country": "Ukraine", "geoPoint": {"lat": 48.61667, "lon": 22.3}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 49.84639, "lon": 37.71861}}, {"city": "Zaporozhye", "country": "Ukraine", "geoPoint": {"lat": 47.82289, "lon": 35.19031}}, {"city": "Belfast", "country": "United Kingdom", "geoPoint": {"lat": 54.59682, "lon": -5.92541}}, {"city": "Blackburn", "country": "United Kingdom", "geoPoint": {"lat": 53.75, "lon": -2.48333}}, {"city": "Bolton", "country": "United Kingdom", "geoPoint": {"lat": 53.58333, "lon": -2.43333}}, {"city": "<PERSON><PERSON>", "country": "United Kingdom", "geoPoint": {"lat": 53.65, "lon": -2.61667}}, {"city": "Derby", "country": "United Kingdom", "geoPoint": {"lat": 52.92277, "lon": -1.47663}}, {"city": "Glasgow", "country": "United Kingdom", "geoPoint": {"lat": 55.86515, "lon": -4.25763}}, {"city": "Hull", "country": "United Kingdom", "geoPoint": {"lat": 53.7446, "lon": -0.33525}}, {"city": "Leicester", "country": "United Kingdom", "geoPoint": {"lat": 52.6386, "lon": -1.13169}}, {"city": "Liverpool", "country": "United Kingdom", "geoPoint": {"lat": 53.41058, "lon": -2.97794}}, {"city": "Londonderry", "country": "United Kingdom", "geoPoint": {"lat": 54.9981, "lon": -7.30934}}, {"city": "London", "country": "United Kingdom", "geoPoint": {"lat": 51.50853, "lon": -0.12574}}, {"city": "Manchester", "country": "United Kingdom", "geoPoint": {"lat": 53.48095, "lon": -2.23743}}, {"city": "Randalstown", "country": "United Kingdom", "geoPoint": {"lat": 54.75, "lon": -6.3}}, {"city": "Salford", "country": "United Kingdom", "geoPoint": {"lat": 53.48771, "lon": -2.29042}}, {"city": "York", "country": "United Kingdom", "geoPoint": {"lat": 53.95763, "lon": -1.08271}}]}