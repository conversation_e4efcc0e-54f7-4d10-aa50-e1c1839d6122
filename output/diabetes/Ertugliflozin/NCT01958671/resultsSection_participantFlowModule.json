{"groups": [{"id": "FG000", "title": "Ertugliflozin 5 mg/Ertugliflozin 5 mg", "description": "Phase A: Ertugliflozin 5 mg administered once daily for 26 weeks. Participants requiring rescue therapy will receive open-label metformin. Phase B: Ertugliflozin 5 mg administered once daily for 26 weeks. Participants not rescued with metformin in Phase A, will receive placebo to metformin. Participants rescued with metformin in Phase A will continue to receive metformin. Participants requiring rescue therapy during Phase B will receive open-label glimepiride."}, {"id": "FG001", "title": "Ertugliflozin 15 mg/Ertugliflozin 15 mg", "description": "Phase A: Ertugliflozin 15 mg administered once daily for 26 weeks. Participants requiring rescue therapy will receive open-label metformin. Phase B: Ertugliflozin 15 mg administered once daily for 26 weeks. Participants not rescued with metformin in Phase A, will receive placebo to metformin. Participants rescued with metformin in Phase A will continue to receive metformin. Participants requiring rescue therapy during Phase B will receive open-label glimepiride."}, {"id": "FG002", "title": "Placebo/Metformin", "description": "Phase A: Placebo to ertugliflozin administered once daily for 26 weeks. Participants requiring rescue therapy will receive open-label metformin. Phase B: Participants not rescued with open-label metformin in Phase A will also receive blinded metformin up to twice daily for 26 weeks in addition to placebo. Participants rescued with metformin in Phase A will continue to receive open-label metformin. Participants requiring rescue therapy during Phase B will receive open-label glimepiride."}], "periods": [{"title": "Overall Study", "milestones": [{"type": "STARTED", "achievements": [{"groupId": "FG000", "numSubjects": "156"}, {"groupId": "FG001", "numSubjects": "152"}, {"groupId": "FG002", "numSubjects": "153"}]}, {"type": "COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "137"}, {"groupId": "FG001", "numSubjects": "128"}, {"groupId": "FG002", "numSubjects": "121"}]}, {"type": "NOT COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "19"}, {"groupId": "FG001", "numSubjects": "24"}, {"groupId": "FG002", "numSubjects": "32"}]}], "dropWithdraws": [{"type": "Adverse Event", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "5"}]}, {"type": "Death", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}]}, {"type": "Excluded medication", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "1"}]}, {"type": "Hyperglycemia", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "2"}]}, {"type": "Lost to Follow-up", "reasons": [{"groupId": "FG000", "numSubjects": "7"}, {"groupId": "FG001", "numSubjects": "11"}, {"groupId": "FG002", "numSubjects": "7"}]}, {"type": "Non-compliance with study drug", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "1"}]}, {"type": "Pregnancy", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "1"}]}, {"type": "Protocol Violation", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "0"}]}, {"type": "Study site terminated by sponsor", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "1"}]}, {"type": "Participant moved", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "1"}]}, {"type": "<PERSON><PERSON><PERSON> by Subject", "reasons": [{"groupId": "FG000", "numSubjects": "8"}, {"groupId": "FG001", "numSubjects": "10"}, {"groupId": "FG002", "numSubjects": "13"}]}]}]}