<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Trials Meta-Analysis Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #3b82f6;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --border-radius: 0.5rem;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }


        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .progress-bar {
            transition: width 0.3s ease;
            border-radius: var(--border-radius);
        }


        .btn {
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .btn-primary {
            background-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card {
            border-radius: var(--border-radius);
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        }

        .header-gradient {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .tab-button {
            background-color: #f8fafc;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }

        .tab-button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
        }

        .tab-button:hover:not(.active) {
            background-color: #e2e8f0;
            color: #475569;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .drug-selection-item {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: #f9fafb;
        }

        .drug-panel {
            transition: all 0.3s ease;
        }

        .drug-panel:hover {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .drug-selection-item.confirmed {
            background: #f0f9ff;
            border-color: #0ea5e9;
        }

        .extraction-status {
            min-height: 2.5rem;
            display: flex;
            align-items: center;
        }

        .status-text {
            line-height: 1.4;
        }

        .drug-selection-item.confirmed input,
        .drug-selection-item.confirmed select {
            background: #e0f2fe;
            color: #0369a1;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-10">
            <h1 class="text-4xl font-bold header-gradient mb-3">Clinical Trials Meta-Analysis Platform</h1>
            <p class="text-gray-500 text-lg">Extract, analyze, and perform meta-analysis on clinical trial data</p>
            <div class="w-24 h-1 bg-blue-500 rounded-full mx-auto mt-4"></div>
        </div>

        <!-- Tab Navigation -->
        <div class="bg-white card p-2 mb-6">
            <div class="flex space-x-1">
                <button id="ai-model-tab" class="tab-button active flex-1 px-6 py-3 text-center font-medium rounded-md transition-all duration-200">
                    🤖 AI Model
                </button>
                <button id="data-tab" class="tab-button flex-1 px-6 py-3 text-center font-medium rounded-md transition-all duration-200">
                    📊 Data Extraction
                </button>
                <button id="meta-analysis-tab" class="tab-button flex-1 px-6 py-3 text-center font-medium rounded-md transition-all duration-200">
                    📈 Meta-Analysis
                </button>
                <button id="network-meta-tab" class="tab-button flex-1 px-6 py-3 text-center font-medium rounded-md transition-all duration-200">
                    🕸️ Network Meta-Analysis
                </button>
            </div>
        </div>

        <!-- Tab Content Container -->
        <div id="tab-content-container">

            <!-- AI Model Tab -->
            <div id="ai-model-content" class="tab-content active">
                <div class="bg-white card p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">🤖 AI Model Configuration</h2>
                        <p class="text-gray-600 text-lg">Configure your local LLM for enhanced drug class detection and analysis</p>
                    </div>

                    <!-- AI Model Configuration -->
                    <div class="max-w-2xl mx-auto space-y-6">
                        <!-- Model Selection -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-blue-800 mb-4">🧠 Model Selection</h3>
                            <div class="space-y-4">
                                <div>
                                    <label for="ai-model-select" class="block text-sm font-medium text-gray-700 mb-2">Local LLM Model:</label>
                                    <select id="ai-model-select" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                                        <option value="">Loading available models...</option>
                                    </select>
                                    <div id="model-status" class="text-sm text-gray-500 mt-1">Connecting to Ollama...</div>
                                </div>
                            </div>
                        </div>

                        <!-- Model Settings -->
                        <div id="model-settings" class="hidden bg-gray-50 border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">⚙️ Model Settings</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="temperature-setting" class="block text-sm font-medium text-gray-700 mb-2">Temperature:</label>
                                    <input type="range" id="temperature-setting" min="0" max="1" step="0.1" value="0.1"
                                           class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>0 (Focused)</span>
                                        <span id="temperature-value" class="font-medium">0.1</span>
                                        <span>1 (Creative)</span>
                                    </div>
                                </div>
                                <div>
                                    <label for="max-tokens-setting" class="block text-sm font-medium text-gray-700 mb-2">Max Tokens:</label>
                                    <select id="max-tokens-setting" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                        <option value="512">512 (Fast)</option>
                                        <option value="1024" selected>1024 (Balanced)</option>
                                        <option value="2048">2048 (Detailed)</option>
                                        <option value="4096">4096 (Comprehensive)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Current Configuration -->
                        <div id="current-config" class="bg-green-50 border border-green-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-green-800 mb-3">📊 Current Configuration</h3>
                            <div id="config-display" class="text-green-700">
                                <p>No model configured. Please select a model above.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Extraction Tab -->
            <div id="data-content" class="tab-content">
                <!-- Project Info -->
                <div class="bg-white card p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">📁 Project Session</h2>
                    <div class="grid grid-cols-1 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Project Name</label>
                            <input type="text" id="project-name-input" placeholder="Enter project name (e.g., diabetes_study_2025)"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <div class="text-xs text-gray-500 mt-1">Custom name for your project folder</div>
                        </div>
                    </div>
                    <div class="flex gap-4">
                        <button id="confirm-project-btn"
                                class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-200">
                            ✅ Confirm Project
                        </button>
                        <button id="reset-project-btn"
                                class="hidden bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md transition duration-200">
                            🔄 Reset Project
                        </button>
                    </div>
                </div>

                <!-- Drug Selection Pane -->
                <div id="drug-selection-section" class="bg-white card p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">🎯 Drug Selection</h2>

                    <!-- Drug Input Section -->
                    <div class="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
                        <div class="mb-4">
                            <label for="drug-input" class="block text-sm font-medium text-gray-700 mb-2">Drug Name or Drug Class</label>
                            <div class="flex gap-2">
                                <input type="text" id="drug-input" placeholder="e.g., Canagliflozin or SGLT2"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button id="start-project-btn"
                                        class="btn btn-primary text-white py-2 px-4 whitespace-nowrap">
                                    🚀 Detect Drug Class
                                </button>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">Enter a drug name or drug class to start extraction</div>
                        </div>
                    </div>

                    <div id="drug-class-info" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md hidden">
                        <!-- Drug class information will be displayed here -->
                    </div>

                    <!-- Available Drugs in Class -->
                    <div id="available-drugs-section" class="hidden mb-6">
                        <h3 class="text-lg font-medium text-gray-700 mb-3">Available Drugs in Class</h3>
                        <div id="available-drugs-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            <!-- Available drugs will be displayed here -->
                        </div>
                    </div>

                    <!-- Selected Drugs List -->
                    <div id="selected-drugs-section" class="mb-6">
                        <div id="selected-drugs-list" class="space-y-3">
                            <!-- Selected drugs will be displayed here -->
                        </div>
                    </div>

                    <!-- Add/Remove Drug Buttons - Inside drug selection pane -->
                    <div class="flex gap-3">
                        <button id="start-extraction-btn" class="hidden px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium">
                            📥 Start Extraction
                        </button>
                        <button id="add-custom-drug-btn" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium">
                            ➕ Add Drug
                        </button>
                        <button id="remove-drug-btn" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md font-medium">
                            ➖ Remove Drug
                        </button>
                    </div>
                </div>
            </div> <!-- End Drug Selection Section -->

            <!-- Extraction Progress -->
        <div id="extraction-progress" class="hidden bg-white card p-6 mb-6 max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-800">Data Extraction Progress</h3>
            </div>
            <div id="extract-output" class="bg-gray-50 p-4 rounded-md font-mono text-sm text-gray-700 whitespace-pre-wrap min-h-[100px] max-h-[400px] overflow-y-auto border">
                Initializing...
            </div>
        </div>

        <!-- Filter Summary -->
        <div id="filter-summary" class="hidden bg-white card p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-800 mb-4">Filter Summary</h3>
            <div id="filter-steps" class="space-y-4"></div>
        </div>

        <!-- Analysis Progress -->
        <div id="analysis-progress" class="hidden bg-white card p-6 mb-8 max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-medium text-gray-800">AI Analysis Progress</h3>
                <span id="analyze-percent" class="text-sm text-gray-600">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div id="analyze-bar" class="progress-bar bg-green-600 h-2 rounded-full" style="width: 0%"></div>
            </div>
            <p id="analyze-text" class="text-sm text-gray-600">Waiting...</p>
        </div>

            </div> <!-- End Data Tab Content -->

            <!-- Meta-Analysis Tab -->
            <div id="meta-analysis-content" class="tab-content">
                <!-- Study Selection Panel -->
                <div class="bg-white card p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">📈 Study Selection</h2>
                    <div class="space-y-4">
                        <div>
                            <label for="drug-folder-meta" class="block text-sm font-medium text-gray-700 mb-2">Select Drug to Analyze</label>
                            <select id="drug-folder-meta" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Loading available drugs...</option>
                            </select>
                            <div id="drug-folder-meta-status" class="text-xs text-gray-500 mt-1">Checking extracted data...</div>
                        </div>
                        <div class="flex gap-4 mt-4">
                            <button id="confirm-project-meta-btn" class="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium">
                                ✅ Confirm Project
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Filter Criteria Panel -->
                <div class="bg-white card p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">🔍 Filter Criteria</h2>

                    <!-- Primary Outcome -->
                    <div id="outcome-section-meta">
                        <label class="block text-sm font-medium text-gray-700 mb-2">🎯 Primary Outcome</label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Category</label>
                                <select id="outcome-section-select-meta" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="adverse_events" selected>Adverse Events</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Search Term</label>
                                <input type="text" id="outcome-term-meta" placeholder="e.g., arrhythmia"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="flex items-end">
                                <button id="discover-terms-btn-meta" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    Discover Terms
                                </button>
                            </div>
                        </div>

                        <!-- Status Display Area -->
                        <div id="discover-status-meta" class="hidden mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                            <div class="animate-pulse text-blue-600">🤖 Using AI to generate a comprehensive list...</div>
                        </div>

                        <!-- Term Selection Area -->
                        <div id="term-selection-meta" class="mt-4 hidden">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Select Acceptable Terms:</label>
                            <div id="term-checkboxes-meta" class="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
                                <!-- Terms will be populated here -->
                            </div>
                            <div class="mt-3 flex gap-2">
                                <button id="confirm-outcome-btn-meta" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                    Confirm
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Invisible Spacing -->
                    <div class="my-6"></div>

                    <!-- Additional Criteria -->
                    <div id="additional-criteria-section-meta" class="hidden mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">🔍 Additional Criteria</label>
                        <div id="criteria-list-meta" class="mt-2">
                            <!-- Additional criteria will be added here -->
                        </div>
                    </div>

                    <!-- Filter Summary -->
                    <div id="filter-summary-meta" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">📊 Filter Summary</label>
                        <div id="filter-steps-meta" class="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm">
                            <!-- Filter steps will be shown here -->
                        </div>
                    </div>

                    <!-- Action Buttons Row -->
                    <div id="filter-action-buttons" class="hidden mt-6 flex gap-3">
                        <button id="confirm-meta-analysis-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium">
                            🚀 Confirm for Meta-analysis
                        </button>
                        <button id="add-criteria-btn-meta" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium">
                            Add Criteria
                        </button>
                        <button id="reset-criteria-btn-meta" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md font-medium">
                            Reset All
                        </button>
                    </div>

                    <div class="text-xs text-gray-500 mt-4">
                        <span id="studies-status-meta">Select a module to filter for associated terms</span>
                    </div>
                </div>

                <!-- Meta-analysis Panel -->
                <div id="meta-analysis-panel" class="hidden bg-white card p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">📊 Meta-analysis</h2>

                    <!-- Meta-analysis Status -->
                    <div id="meta-analysis-status" class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                        <div class="text-blue-800 font-medium">Ready for Meta-analysis</div>
                        <div id="meta-analysis-details" class="text-blue-600 text-sm mt-1">
                            <!-- Details will be populated here -->
                        </div>
                    </div>

                    <!-- Meta-analysis Actions -->
                    <div class="space-y-4">
                        <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                            <h3 class="font-medium text-gray-800 mb-2">📁 Study Preparation</h3>
                            <p class="text-gray-600 text-sm mb-3">Studies will be copied to the meta-analysis workspace for R processing.</p>
                            <div id="copy-progress" class="hidden mb-3">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div id="copy-progress-bar" class="bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                                <div id="copy-status" class="text-sm text-gray-600 mt-1">Preparing studies...</div>
                            </div>
                            <button id="prepare-studies-btn" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium">
                                📋 Prepare Studies for Analysis
                            </button>
                        </div>

                        <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                            <h3 class="font-medium text-gray-800 mb-2">📊 Table 1: Study Characteristics</h3>
                            <p class="text-gray-600 text-sm mb-3">Generate comprehensive characteristics table for all included studies.</p>
                            <button id="generate-table1-btn" class="hidden px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md font-medium">
                                📋 Generate Table 1
                            </button>
                        </div>

                        <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                            <h3 class="font-medium text-gray-800 mb-2">🔬 R Meta-analysis</h3>
                            <p class="text-gray-600 text-sm mb-3">Run statistical meta-analysis using R with forest plots and heterogeneity assessment.</p>
                            <button id="run-meta-analysis-btn" class="hidden px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium">
                                📈 Run Meta-analysis
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Analysis Progress -->
                <div id="analysis-progress-meta" class="hidden bg-white card p-6 mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-lg font-medium text-gray-800">AI Analysis Progress</h3>
                        <span id="analyze-percent-meta" class="text-sm text-gray-600">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div id="analyze-bar-meta" class="progress-bar bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                    <p id="analyze-text-meta" class="text-sm text-gray-600">Waiting...</p>
                </div>

            </div>

            <!-- Network Meta-Analysis Tab -->
            <div id="network-meta-content" class="tab-content">
                <div class="bg-white card p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">🕸️ Network Meta-Analysis</h2>
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">🔬</div>
                        <h3 class="text-2xl font-semibold text-gray-700 mb-2">Advanced Analytics</h3>
                        <p class="text-gray-500 mb-6">Network meta-analysis for comparing multiple interventions.</p>
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 max-w-2xl mx-auto">
                            <h4 class="font-semibold text-purple-800 mb-2">Planned Features:</h4>
                            <ul class="text-left text-purple-700 space-y-1">
                                <li>• Network plots and diagrams</li>
                                <li>• Indirect comparison analysis</li>
                                <li>• Ranking probabilities (SUCRA)</li>
                                <li>• Consistency assessment</li>
                                <li>• Treatment ranking tables</li>
                            </ul>
                        </div>
                    </div>
                </div>

            </div>

        </div> <!-- End Tab Content Container -->
    </div>

    <script>
        /**
         * Clinical Trials Meta-Analysis Platform
         * =====================================
         *
         * Main JavaScript application for the clinical trials analysis platform.
         * Organized into logical sections for better maintainability.
         *
         * Sections:
         * 1. Global State & Configuration
         * 2. DOM Element References
         * 3. Tab Management
         * 4. AI Model Management
         * 5. Project Management
         * 6. Drug Class Detection
         * 7. Data Extraction
         * 8. Meta-Analysis & Filtering
         * 9. Utility Functions
         * 10. Event Listeners & Initialization
         */

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Clinical Trials Platform - Initializing...');

            // ========================================
            // 1. GLOBAL STATE & CONFIGURATION
            // ========================================

            // Global state for progressive filtering
            let currentCriteria = [];
            let filteredStudies = [];
            let criteriaIdCounter = 1;
            let allStudies = [];

            // Global state for project management
            let currentSession = null;
            let selectedDrugs = [];
            let drugCounter = 1;
            let lastOutputLength = 0;
            let selectedAiModel = null;
            let lastDisplayedIndex = 0;

            // ========================================
            // 2. DOM ELEMENT REFERENCES
            // ========================================

            // Tab elements
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            // AI Model tab elements
            const aiModelSelect = document.getElementById('ai-model-select');
            const modelStatus = document.getElementById('model-status');
            const modelSettings = document.getElementById('model-settings');
            const temperatureSetting = document.getElementById('temperature-setting');
            const temperatureValue = document.getElementById('temperature-value');
            const maxTokensSetting = document.getElementById('max-tokens-setting');
            const configDisplay = document.getElementById('config-display');

            // New Data tab elements
            const drugInput = document.getElementById('drug-input');
            const projectNameInput = document.getElementById('project-name-input');
            const startProjectBtn = document.getElementById('start-project-btn');
            const confirmProjectBtn = document.getElementById('confirm-project-btn');
            const resetProjectBtn = document.getElementById('reset-project-btn');
            const drugSelectionSection = document.getElementById('drug-selection-section');
            const addCustomDrugBtn = document.getElementById('add-custom-drug-btn');
            const startExtractionBtn = document.getElementById('start-extraction-btn');

            // Meta-analysis tab elements
            const drugFolderSelect = document.getElementById('drug-folder-meta');
            const drugFolderStatus = document.getElementById('drug-folder-meta-status');

            // Legacy elements (keeping for compatibility)
            const extractBtn = document.getElementById('extract-btn');
            const stopExtractBtn = document.getElementById('stop-extract-btn');
            const extractionProgress = document.getElementById('extraction-progress');
            const analysisProgress = document.getElementById('analysis-progress');

            // ========================================
            // 4. AI MODEL MANAGEMENT
            // ========================================

            /**
             * Initialize AI model selection and configuration
             */
            function initializeAiModel() {
                if (!aiModelSelect) return;

                aiModelSelect.addEventListener('change', function() {
                    const selectedModel = this.value;
                    if (selectedModel) {
                        selectedAiModel = selectedModel;
                        modelSettings.classList.remove('hidden');
                        updateConfigDisplay();
                        saveAiConfiguration();
                    } else {
                        selectedAiModel = null;
                        modelSettings.classList.add('hidden');
                        updateConfigDisplay();
                    }
                });
            }

            /**
             * Initialize temperature and token settings
             */
            function initializeModelSettings() {
                if (temperatureSetting) {
                    temperatureSetting.addEventListener('input', function() {
                        temperatureValue.textContent = this.value;
                        if (selectedAiModel) {
                            saveAiConfiguration();
                        }
                        updateConfigDisplay();
                    });
                }

                if (maxTokensSetting) {
                    maxTokensSetting.addEventListener('input', function() {
                        if (selectedAiModel) {
                            saveAiConfiguration();
                        }
                        updateConfigDisplay();
                    });
                }
            }

            // ========================================
            // 5. PROJECT MANAGEMENT
            // ========================================

            /**
             * Initialize project management functionality
             */
            function initializeProjectManagement() {
                // Project confirmation
                if (confirmProjectBtn) {
                    confirmProjectBtn.addEventListener('click', confirmProject);
                }

                // Project reset
                if (resetProjectBtn) {
                    resetProjectBtn.addEventListener('click', resetProject);
                }

                // Start new project
                if (startProjectBtn) {
                    startProjectBtn.addEventListener('click', startNewProject);
                }

                // Add drug selection
                if (addCustomDrugBtn) {
                    addCustomDrugBtn.addEventListener('click', addDrugSelection);
                }

                // Start extraction
                if (startExtractionBtn) {
                    startExtractionBtn.addEventListener('click', startBatchExtraction);
                }
            }

            /**
             * Initialize remove drug functionality
             */
            function initializeRemoveDrug() {
                const removeDrugBtn = document.getElementById('remove-drug-btn');
                if (removeDrugBtn) {
                    removeDrugBtn.addEventListener('click', removeDrugSelection);
                }
            }

            // ========================================
            // 6. META-ANALYSIS MANAGEMENT
            // ========================================

            /**
             * Initialize meta-analysis tab functionality
             */
            function initializeMetaAnalysis() {
                const discoverTermsBtnMeta = document.getElementById('discover-terms-btn-meta');
                const confirmOutcomeBtnMeta = document.getElementById('confirm-outcome-btn-meta');
                const addCriteriaBtnMeta = document.getElementById('add-criteria-btn-meta');
                const resetCriteriaBtnMeta = document.getElementById('reset-criteria-btn-meta');
                const confirmProjectMetaBtn = document.getElementById('confirm-project-meta-btn');
                const confirmMetaAnalysisBtn = document.getElementById('confirm-meta-analysis-btn');
                const prepareStudiesBtn = document.getElementById('prepare-studies-btn');
                const generateTable1Btn = document.getElementById('generate-table1-btn');
                const runMetaAnalysisBtn = document.getElementById('run-meta-analysis-btn');

                if (discoverTermsBtnMeta) {
                    discoverTermsBtnMeta.addEventListener('click', discoverTermsMeta);
                }
                if (confirmOutcomeBtnMeta) {
                    confirmOutcomeBtnMeta.addEventListener('click', confirmOutcomeMeta);
                }

                if (addCriteriaBtnMeta) {
                    addCriteriaBtnMeta.addEventListener('click', addCriteriaMeta);
                }
                if (resetCriteriaBtnMeta) {
                    resetCriteriaBtnMeta.addEventListener('click', resetCriteriaMeta);
                }
                if (confirmProjectMetaBtn) {
                    confirmProjectMetaBtn.addEventListener('click', confirmProjectMeta);
                }
                if (confirmMetaAnalysisBtn) {
                    confirmMetaAnalysisBtn.addEventListener('click', confirmMetaAnalysis);
                }
                if (prepareStudiesBtn) {
                    prepareStudiesBtn.addEventListener('click', prepareStudiesForAnalysis);
                }
                if (generateTable1Btn) {
                    generateTable1Btn.addEventListener('click', generateTable1);
                }
                if (runMetaAnalysisBtn) {
                    runMetaAnalysisBtn.addEventListener('click', runMetaAnalysis);
                }
            }

            // Add debugging for start extraction button
            if (startExtractionBtn) {
                startExtractionBtn.addEventListener('click', function() {
                    try {
                        console.log('[DEBUG] Start extraction button clicked');
                        startBatchExtraction();
                    } catch (error) {
                        console.error('[ERROR] Failed to start extraction:', error);
                        const extractOutput = document.getElementById('extract-output');
                        if (extractOutput) {
                            extractOutput.textContent = `Error starting extraction: ${error.message}`;
                        }
                    }
                });
                console.log('[DEBUG] Start extraction button event listener added');
            } else {
                console.error('[ERROR] Start extraction button not found');
                throw new Error('Start extraction button element not found in DOM');
            }

            // Data extraction tab initialization complete





            // Meta-Analysis Tab Event Listeners
            const discoverTermsBtnMeta = document.getElementById('discover-terms-btn-meta');
            const confirmOutcomeBtnMeta = document.getElementById('confirm-outcome-btn-meta');
            const addCriteriaBtnMeta = document.getElementById('add-criteria-btn-meta');
            const resetCriteriaBtnMeta = document.getElementById('reset-criteria-btn-meta');

            if (discoverTermsBtnMeta) {
                discoverTermsBtnMeta.addEventListener('click', discoverTermsMeta);
            }
            if (confirmOutcomeBtnMeta) {
                confirmOutcomeBtnMeta.addEventListener('click', confirmOutcomeMeta);
            }

            if (addCriteriaBtnMeta) {
                addCriteriaBtnMeta.addEventListener('click', addCriteriaMeta);
            }
            if (resetCriteriaBtnMeta) {
                resetCriteriaBtnMeta.addEventListener('click', resetCriteriaMeta);
            }

            // ========================================
            // 7. WINDOW EVENT HANDLERS
            // ========================================

            // Stop processes when page is closed
            window.addEventListener('beforeunload', function(e) {
                const extractionRunning = !stopExtractBtn.classList.contains('hidden');
                const analysisRunning = false; // Analysis button removed

                if (extractionRunning || analysisRunning) {
                    // Try to stop the process
                    fetch('/api/stop', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    // Show confirmation dialog
                    e.preventDefault();
                    const operation = extractionRunning ? 'Extraction' : 'Analysis';
                    e.returnValue = `${operation} is running. Are you sure you want to leave?`;
                    return e.returnValue;
                }
            });

            // ========================================
            // 8. PROJECT MANAGEMENT FUNCTIONS
            // ========================================

            /**
             * Confirm project creation with validation
             */
            function confirmProject() {
                console.log('🚀 confirmProject called');
                const projectName = document.getElementById('project-name-input').value.trim();
                console.log('📝 projectName:', projectName);

                if (!projectName) {
                    alert('Please enter a project name');
                    return;
                }

                // Validate project name (basic validation)
                const validProjectName = projectName.replace(/[^\w\-_.]/g, '_');
                if (validProjectName !== projectName) {
                    console.log(`🔧 Project name sanitized: ${projectName} -> ${validProjectName}`);
                    document.getElementById('project-name-input').value = validProjectName;
                }

                // Create project folder
                fetch('/api/create-project', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        project_name: validProjectName
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI - lock project name and show reset button
                        confirmProjectBtn.classList.add('hidden');
                        resetProjectBtn.classList.remove('hidden');
                        projectNameInput.disabled = true;
                        projectNameInput.classList.add('bg-gray-100', 'cursor-not-allowed');

                        // Initialize current session
                        currentSession = {
                            projectName: validProjectName,
                            timestamp: new Date().toISOString()
                        };

                        console.log(`✅ Project confirmed: ${validProjectName}`);
                        console.log(`📁 Project folder created at: output/${validProjectName}`);
                    } else {
                        alert(`Failed to create project: ${data.message}`);
                    }
                })
                .catch(error => {
                    console.error('Error creating project:', error);
                    alert('Failed to create project folder');
                });
            }

            function startNewProject() {
                console.log('startNewProject called');
                const drugInputElement = document.getElementById('drug-input');
                const drugInput = drugInputElement.value.trim();

                if (!drugInput) {
                    alert('Please enter a drug name or drug class');
                    return;
                }

                if (!currentSession || !currentSession.projectName) {
                    alert('Please confirm a project name first');
                    return;
                }

                // Update current session with drug input
                currentSession.inputDrug = drugInput;

                // Update UI - grey out and disable the input field
                startProjectBtn.classList.add('hidden');
                drugInputElement.disabled = true;
                drugInputElement.classList.add('bg-gray-100', 'cursor-not-allowed', 'text-gray-500');

                // Detect drug class and show selection
                detectDrugClass(drugInput);

                console.log(`🔍 Analyzing input: ${drugInput} for project: ${currentSession.projectName}`);
            }

            function resetProject() {
                if (!confirm('Are you sure you want to reset the project? This will delete the project folder and clear all current data.')) {
                    return;
                }

                const projectName = currentSession ? currentSession.projectName : null;

                // Delete project folder if it exists
                if (projectName) {
                    fetch('/api/delete-project', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            project_name: projectName
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            console.log(`🗑️ Project folder deleted: output/${projectName}`);
                        } else {
                            console.log(`⚠️ Failed to delete project folder: ${data.message}`);
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting project:', error);
                    });
                }

                // Reset UI
                currentSession = null;
                selectedDrugs = [];
                drugCounter = 1;

                // Reset project name section
                confirmProjectBtn.classList.remove('hidden');
                resetProjectBtn.classList.add('hidden');
                projectNameInput.disabled = false;
                projectNameInput.classList.remove('bg-gray-100', 'cursor-not-allowed');
                projectNameInput.value = '';

                // Reset original drug selection panel
                const drugInputElement = document.getElementById('drug-input');
                startProjectBtn.classList.remove('hidden');
                drugInputElement.disabled = false;
                drugInputElement.classList.remove('bg-gray-100', 'cursor-not-allowed', 'text-gray-500');
                drugInputElement.value = '';
                document.getElementById('drug-class-info').classList.add('hidden');
                document.getElementById('available-drugs-grid').innerHTML = '';
                document.getElementById('selected-drugs-list').innerHTML = '';
                document.getElementById('available-drugs-section').classList.add('hidden');
                startExtractionBtn.classList.add('hidden');

                // Remove all additional drug selection panels
                const additionalPanels = document.querySelectorAll('[id^="drug-selection-panel-"]');
                additionalPanels.forEach(panel => {
                    panel.remove();
                    console.log(`🗑️ Removed additional panel: ${panel.id}`);
                });

                // Hide extraction progress section
                const extractionProgress = document.getElementById('extraction-progress');
                if (extractionProgress) {
                    extractionProgress.classList.add('hidden');
                }

                // Clear extraction output
                const extractOutput = document.getElementById('extract-output');
                if (extractOutput) {
                    extractOutput.textContent = 'Initializing...';
                }

                console.log('🔄 Project reset - entire data extraction tab refreshed to beginning');
            }

            function detectDrugClass(inputDrug) {
                // Show loading state
                const drugClassInfo = document.getElementById('drug-class-info');
                drugClassInfo.innerHTML = '<div class="animate-pulse text-blue-600">🤖 Using AI to generate a comprehensive list...</div>';
                drugClassInfo.classList.remove('hidden');

                // Get AI model configuration
                const aiConfig = getAiModelConfiguration();

                if (!aiConfig.model) {
                    drugClassInfo.innerHTML = '<div class="text-red-600">❌ Please configure your AI model in the AI Model tab first</div>';
                    return;
                }

                console.log('Using AI configuration:', aiConfig);

                // Call backend with AI configuration
                fetch('/api/detect-drug-class', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        drug_input: inputDrug,
                        ai_config: aiConfig
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('LLM drug class detection response:', data);
                    if (data.success) {
                        displayDrugClassInfo(data);
                        // Always show available drugs for drug classes
                        if (data.is_drug_class && data.drugs) {
                            displayAvailableDrugs(data.drugs);
                        }
                    } else {
                        drugClassInfo.innerHTML = `<div class="text-red-600">❌ ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('LLM drug class detection error:', error);
                    drugClassInfo.innerHTML = '<div class="text-red-600">❌ Error connecting to LLM for drug class detection</div>';
                });
            }

            function getAiModelConfiguration() {
                // Get AI model configuration from localStorage or current settings
                const savedConfig = localStorage.getItem('aiModelConfig');
                if (savedConfig) {
                    try {
                        return JSON.parse(savedConfig);
                    } catch (error) {
                        console.error('Error parsing AI config:', error);
                    }
                }

                // Fallback to current form values if available
                if (selectedAiModel && temperatureSetting && maxTokensSetting) {
                    return {
                        model: selectedAiModel,
                        temperature: parseFloat(temperatureSetting.value),
                        maxTokens: parseInt(maxTokensSetting.value)
                    };
                }

                return {};
            }

            function displayDrugClassInfo(data) {
                const drugClassInfo = document.getElementById('drug-class-info');
                const availableDrugsSection = document.getElementById('available-drugs-section');

                console.log('displayDrugClassInfo received:', data);

                if (data.is_single_drug) {
                    drugClassInfo.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span class="text-blue-600 font-medium">💊 Single Drug Detected:</span>
                            <span class="font-semibold">${data.detected_drug}</span>
                            <span class="text-gray-500">(${data.study_count} studies available)</span>
                        </div>
                    `;
                    availableDrugsSection.classList.add('hidden');
                    // Auto-add the single drug to selection
                    addDrugToSelectedList(data.detected_drug, data.study_count);
                } else if (data.is_drug_class) {
                    // Handle LLM drug class detection format
                    const drugCount = data.drugs ? data.drugs.length : 0;
                    const drugsWithStudies = data.drugs_with_actual_studies || 0;

                    drugClassInfo.innerHTML = `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <span class="text-green-600 font-medium">🤖 LLM Detected Drug Class:</span>
                                <span class="font-semibold text-green-800">${data.drug_class}</span>
                            </div>
                            <div class="text-sm text-green-700 space-y-1">
                                <p><strong>Description:</strong> ${data.description || 'N/A'}</p>
                                <p><strong>Drugs with clinical trials:</strong> ${drugsWithStudies}</p>

                                ${data.reasoning ? `<p><strong>Reasoning:</strong> ${data.reasoning}</p>` : ''}
                            </div>
                        </div>
                    `;
                    availableDrugsSection.classList.remove('hidden');
                    displayAvailableDrugs(data.drugs || []);
                } else {
                    // Fallback for other formats
                    const availableDrugs = data.available_drugs || data.drugs || [];
                    drugClassInfo.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span class="text-blue-600 font-medium">🏷️ Drug Class Detected:</span>
                            <span class="font-semibold">${data.drug_class || 'Unknown'}</span>
                            <span class="text-gray-500">(${availableDrugs.length} drugs available)</span>
                        </div>
                    `;
                    availableDrugsSection.classList.remove('hidden');
                    displayAvailableDrugs(availableDrugs);
                }
            }

            function displayAvailableDrugs(availableDrugs) {
                const availableDrugsGrid = document.getElementById('available-drugs-grid');
                availableDrugsGrid.innerHTML = '';

                console.log('displayAvailableDrugs received:', availableDrugs);

                if (!availableDrugs || availableDrugs.length === 0) {
                    availableDrugsGrid.innerHTML = '<div class="text-gray-500 text-center py-4">No drugs available</div>';
                    return;
                }

                availableDrugs.forEach(drug => {
                    // Handle LLM response format {name, study_count}
                    const drugName = drug.name || drug;
                    const studyCount = drug.study_count;

                    // Determine styling based on study availability
                    const hasStudies = studyCount !== 'N/A' && studyCount > 0;
                    const cardClass = hasStudies
                        ? 'border border-green-200 bg-green-50 rounded-lg p-3 hover:bg-green-100 cursor-pointer transition-colors'
                        : 'border border-gray-200 bg-gray-50 rounded-lg p-3 cursor-not-allowed opacity-60';

                    const studyText = studyCount === 'N/A' ? 'N/A (no studies)' : `${studyCount} studies`;
                    const textColor = hasStudies ? 'text-green-600' : 'text-gray-500';

                    const drugCard = document.createElement('div');
                    drugCard.className = cardClass;
                    drugCard.innerHTML = `
                        <div class="flex items-center space-x-2">
                            <input type="checkbox" id="drug-${drugName}" class="drug-checkbox rounded"
                                   data-drug-name="${drugName}" data-study-count="${studyCount}"
                                   ${!hasStudies ? 'disabled' : ''}>
                            <label for="drug-${drugName}" class="cursor-pointer flex-1">
                                <div class="font-medium text-gray-800">${drugName}</div>
                                <div class="text-sm ${textColor}">${studyText}</div>
                            </label>
                        </div>
                    `;

                    // Add click handler for the entire card
                    drugCard.addEventListener('click', function(e) {
                        if (e.target.type !== 'checkbox') {
                            const checkbox = drugCard.querySelector('.drug-checkbox');
                            checkbox.checked = !checkbox.checked;
                            checkbox.dispatchEvent(new Event('change'));
                        }
                    });

                    // Add change handler for checkbox
                    const checkbox = drugCard.querySelector('.drug-checkbox');
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            addDrugToSelectedList(drug.name, drug.study_count);
                        } else {
                            removeDrugFromSelectedList(drug.name);
                        }
                    });

                    availableDrugsGrid.appendChild(drugCard);
                });


            }

            function detectDrugClassForPanel(inputDrug, panelId) {
                // Show loading state for this specific panel
                const drugClassInfo = document.getElementById(`drug-class-info-${panelId}`);
                drugClassInfo.innerHTML = '<div class="animate-pulse text-blue-600">🤖 Using AI to generate a comprehensive list...</div>';
                drugClassInfo.classList.remove('hidden');

                // Get AI model configuration
                const aiConfig = getAiModelConfiguration();

                if (!aiConfig.model) {
                    drugClassInfo.innerHTML = '<div class="text-red-600">❌ Please configure your AI model in the AI Model tab first</div>';
                    return;
                }

                console.log('Using AI configuration for panel:', panelId, aiConfig);

                // Call backend with AI configuration
                fetch('/api/detect-drug-class', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        drug_input: inputDrug,
                        ai_config: aiConfig
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('LLM drug class detection response for panel:', panelId, data);
                    if (data.success) {
                        displayDrugClassInfoForPanel(data, panelId);
                        // Always show available drugs for drug classes
                        if (data.is_drug_class && data.drugs) {
                            displayAvailableDrugsForPanel(data.drugs, panelId);
                        }
                    } else {
                        drugClassInfo.innerHTML = `<div class="text-red-600">❌ ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('LLM drug class detection error for panel:', panelId, error);
                    drugClassInfo.innerHTML = '<div class="text-red-600">❌ Error connecting to LLM for drug class detection</div>';
                });
            }

            function displayDrugClassInfoForPanel(data, panelId) {
                const drugClassInfo = document.getElementById(`drug-class-info-${panelId}`);
                const availableDrugsSection = document.getElementById(`available-drugs-section-${panelId}`);

                console.log('displayDrugClassInfoForPanel received:', data, 'for panel:', panelId);

                if (data.is_single_drug) {
                    drugClassInfo.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span class="text-blue-600 font-medium">💊 Single Drug Detected:</span>
                            <span class="font-semibold">${data.detected_drug}</span>
                            <span class="text-gray-500">(${data.study_count} studies available)</span>
                        </div>
                    `;
                    availableDrugsSection.classList.add('hidden');
                    // Auto-add the single drug to selection
                    addDrugToSelectedListForPanel(data.detected_drug, data.study_count, panelId);
                } else if (data.is_drug_class) {
                    // Handle LLM drug class detection format
                    const drugCount = data.drugs ? data.drugs.length : 0;
                    const drugsWithStudies = data.drugs_with_actual_studies || 0;

                    drugClassInfo.innerHTML = `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <span class="text-green-600 font-medium">🤖 LLM Detected Drug Class:</span>
                                <span class="font-semibold text-green-800">${data.drug_class}</span>
                            </div>
                            <div class="text-sm text-green-700 space-y-1">
                                <p><strong>Description:</strong> ${data.description || 'N/A'}</p>
                                <p><strong>Drugs with clinical trials:</strong> ${drugsWithStudies}</p>

                                ${data.reasoning ? `<p><strong>Reasoning:</strong> ${data.reasoning}</p>` : ''}
                            </div>
                        </div>
                    `;
                    availableDrugsSection.classList.remove('hidden');
                    displayAvailableDrugsForPanel(data.drugs || [], panelId);
                } else {
                    // Fallback for other formats
                    const availableDrugs = data.available_drugs || data.drugs || [];
                    drugClassInfo.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span class="text-blue-600 font-medium">🏷️ Drug Class Detected:</span>
                            <span class="font-semibold">${data.drug_class || 'Unknown'}</span>
                            <span class="text-gray-500">(${availableDrugs.length} drugs available)</span>
                        </div>
                    `;
                    availableDrugsSection.classList.remove('hidden');
                    displayAvailableDrugsForPanel(availableDrugs, panelId);
                }
            }

            function displayAvailableDrugsForPanel(availableDrugs, panelId) {
                const availableDrugsGrid = document.getElementById(`available-drugs-grid-${panelId}`);
                availableDrugsGrid.innerHTML = '';

                console.log('displayAvailableDrugsForPanel received:', availableDrugs, 'for panel:', panelId);

                if (!availableDrugs || availableDrugs.length === 0) {
                    availableDrugsGrid.innerHTML = '<div class="text-gray-500 text-center py-4">No drugs available</div>';
                    return;
                }

                availableDrugs.forEach(drug => {
                    // Handle LLM response format {name, study_count}
                    const drugName = drug.name || drug;
                    const studyCount = drug.study_count;

                    // Determine styling based on study availability
                    const hasStudies = studyCount !== 'N/A' && studyCount > 0;
                    const cardClass = hasStudies
                        ? 'border border-green-200 bg-green-50 rounded-lg p-3 hover:bg-green-100 cursor-pointer transition-colors'
                        : 'border border-gray-200 bg-gray-50 rounded-lg p-3 cursor-not-allowed opacity-60';

                    const studyText = studyCount === 'N/A' ? 'N/A (no studies)' : `${studyCount} studies`;
                    const textColor = hasStudies ? 'text-green-600' : 'text-gray-500';

                    const drugCard = document.createElement('div');
                    drugCard.className = cardClass;
                    drugCard.innerHTML = `
                        <div class="flex items-center space-x-2">
                            <input type="checkbox" id="drug-${drugName}-${panelId}" class="drug-checkbox-${panelId} rounded"
                                   data-drug-name="${drugName}" data-study-count="${studyCount}"
                                   ${!hasStudies ? 'disabled' : ''}>
                            <label for="drug-${drugName}-${panelId}" class="cursor-pointer flex-1">
                                <div class="font-medium text-gray-800">${drugName}</div>
                                <div class="text-sm ${textColor}">${studyText}</div>
                            </label>
                        </div>
                    `;

                    // Add click handler for the entire card
                    drugCard.addEventListener('click', function(e) {
                        if (e.target.type !== 'checkbox') {
                            const checkbox = drugCard.querySelector(`.drug-checkbox-${panelId}`);
                            checkbox.checked = !checkbox.checked;
                            checkbox.dispatchEvent(new Event('change'));
                        }
                    });

                    // Add change handler for checkbox
                    const checkbox = drugCard.querySelector(`.drug-checkbox-${panelId}`);
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            addDrugToSelectedListForPanel(drug.name, drug.study_count, panelId);
                        } else {
                            removeDrugFromSelectedListForPanel(drug.name, panelId);
                        }
                    });

                    availableDrugsGrid.appendChild(drugCard);
                });


            }

            function addDrugToSelectedListForPanel(drugName, studyCount, panelId) {
                // Extract generic name for processing (before parentheses)
                const genericName = drugName.includes('(') ? drugName.split('(')[0].trim() : drugName;

                // Check if drug is already in the global list (by generic name)
                if (selectedDrugs.find(drug => {
                    const existingGeneric = drug.name.includes('(') ? drug.name.split('(')[0].trim() : drug.name;
                    return existingGeneric === genericName;
                })) {
                    return;
                }

                const selectedDrugsList = document.getElementById(`selected-drugs-list-${panelId}`);
                const drugId = `selected-drug-${drugCounter++}`;

                const drugItem = document.createElement('div');
                drugItem.className = 'border border-blue-200 rounded-lg p-4 bg-blue-50';
                drugItem.id = drugId;
                drugItem.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="font-medium text-gray-800">${drugName}</div>
                            <div class="text-sm text-gray-500">${studyCount} studies available</div>
                            <div class="text-xs text-blue-600">Will search for: ${genericName}</div>
                        </div>
                    </div>
                    <div class="extraction-status mt-3">
                        <div class="status-text text-sm text-gray-600">Ready to extract...</div>
                    </div>
                `;

                selectedDrugsList.appendChild(drugItem);

                // Add to selectedDrugs array (auto-confirmed)
                selectedDrugs.push({
                    id: drugId,
                    name: drugName,
                    genericName: genericName,  // Store generic name for extraction
                    studyCount: studyCount,
                    confirmed: true,  // Auto-confirm drugs when added
                    panelId: panelId
                });

                // No event listeners needed since buttons are removed

                updateExtractionButton();
            }

            function removeDrugFromSelectedListForPanel(drugName, panelId) {
                // Find and remove from selectedDrugs array
                selectedDrugs = selectedDrugs.filter(drug => drug.name !== drugName);

                // Remove from DOM
                const selectedDrugsList = document.getElementById(`selected-drugs-list-${panelId}`);
                const drugItems = selectedDrugsList.querySelectorAll('div');
                drugItems.forEach(item => {
                    const nameElement = item.querySelector('.font-medium');
                    if (nameElement && nameElement.textContent === drugName) {
                        item.remove();
                    }
                });

                // Uncheck in available drugs if present
                const checkbox = document.getElementById(`drug-${drugName}-${panelId}`);
                if (checkbox) {
                    checkbox.checked = false;
                }

                updateExtractionButton();
            }

            function addDrugToSelectedList(drugName, studyCount) {
                // Extract generic name for processing (before parentheses)
                const genericName = drugName.includes('(') ? drugName.split('(')[0].trim() : drugName;

                // Check if drug is already in the list (by generic name)
                if (selectedDrugs.find(drug => {
                    const existingGeneric = drug.name.includes('(') ? drug.name.split('(')[0].trim() : drug.name;
                    return existingGeneric === genericName;
                })) {
                    return;
                }

                const selectedDrugsList = document.getElementById('selected-drugs-list');
                const drugId = `selected-drug-${drugCounter++}`;

                const drugItem = document.createElement('div');
                drugItem.className = 'border border-blue-200 rounded-lg p-4 bg-blue-50';
                drugItem.id = drugId;
                drugItem.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="font-medium text-gray-800">${drugName}</div>
                            <div class="text-sm text-gray-500">${studyCount} studies available</div>
                            <div class="text-xs text-blue-600">Will search for: ${genericName}</div>
                        </div>
                    </div>
                    <div class="extraction-status mt-3">
                        <div class="status-text text-sm text-gray-600">Ready to extract...</div>
                    </div>
                `;

                selectedDrugsList.appendChild(drugItem);

                // Add to selectedDrugs array (auto-confirmed)
                selectedDrugs.push({
                    id: drugId,
                    name: drugName,
                    genericName: genericName,  // Store generic name for extraction
                    studyCount: studyCount,
                    confirmed: true  // Auto-confirm drugs when added
                });

                // No event listeners needed since buttons are removed

                updateExtractionButton();
            }

            function removeDrugFromSelectedList(drugName) {
                // Find and remove from selectedDrugs array
                selectedDrugs = selectedDrugs.filter(drug => drug.name !== drugName);

                // Remove from DOM
                const drugItems = document.querySelectorAll('#selected-drugs-list > div');
                drugItems.forEach(item => {
                    const nameElement = item.querySelector('.font-medium');
                    if (nameElement && nameElement.textContent === drugName) {
                        item.remove();
                    }
                });

                // Uncheck in available drugs if present
                const checkbox = document.getElementById(`drug-${drugName}`);
                if (checkbox) {
                    checkbox.checked = false;
                }

                updateExtractionButton();
            }

            // confirmSelectedDrug function removed - drugs are now auto-confirmed when added

            function addDrugSelection() {
                console.log('addDrugSelection called, currentSession:', currentSession);
                console.log('currentSession exists:', !!currentSession);
                console.log('currentSession.projectName:', currentSession?.projectName);

                // Create a new drug selection panel at the bottom
                if (currentSession && currentSession.projectName) {
                    console.log('Creating new drug selection panel...');
                    createNewDrugSelectionPanel();
                    console.log('➕ Added new drug selection panel');
                } else {
                    console.log('Session check failed - currentSession:', currentSession);
                    alert('Please confirm a project name first');
                }
            }

            function createNewDrugSelectionPanel() {
                const panelId = `drug-selection-panel-${drugCounter++}`;
                console.log('Creating new panel with ID:', panelId);
                const dataExtractionTab = document.getElementById('data-content');
                console.log('dataExtractionTab found:', !!dataExtractionTab);

                // Create new drug selection panel - exact copy of the original
                const newPanel = document.createElement('div');
                newPanel.id = panelId;
                newPanel.className = 'bg-white card p-6 mb-6';
                newPanel.innerHTML = `
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">🎯 Drug Selection</h2>

                    <!-- Drug Input Section -->
                    <div id="drug-input-section-${panelId}" class="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
                        <div class="mb-4">
                            <label for="drug-input-${panelId}" class="block text-sm font-medium text-gray-700 mb-2">Drug Name or Drug Class</label>
                            <div class="flex gap-2">
                                <input type="text" id="drug-input-${panelId}" placeholder="e.g., Canagliflozin or SGLT2"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button id="start-project-btn-${panelId}"
                                        class="btn btn-primary text-white py-2 px-4 whitespace-nowrap">
                                    🚀 Detect Drug Class
                                </button>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">Enter a drug name or drug class to start extraction</div>
                        </div>
                    </div>

                    <div id="drug-class-info-${panelId}" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md hidden">
                        <!-- Drug class information will be displayed here -->
                    </div>

                    <!-- Available Drugs in Class -->
                    <div id="available-drugs-section-${panelId}" class="hidden mb-6">
                        <h3 class="text-lg font-medium text-gray-700 mb-3">Available Drugs in Class</h3>
                        <div id="available-drugs-grid-${panelId}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            <!-- Available drugs will be displayed here -->
                        </div>
                    </div>

                    <!-- Selected Drugs List -->
                    <div id="selected-drugs-section-${panelId}" class="mb-6">
                        <div id="selected-drugs-list-${panelId}" class="space-y-3">
                            <!-- Selected drugs will be displayed here -->
                        </div>
                    </div>

                    <!-- Add/Remove Drug Buttons - Inside drug selection pane -->
                    <div class="flex gap-3">
                        <button id="start-extraction-btn-${panelId}" class="hidden px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium">
                            📥 Start Extraction
                        </button>
                        <button id="add-custom-drug-btn-${panelId}" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium">
                            ➕ Add Drug
                        </button>
                        <button id="remove-drug-btn-${panelId}" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md font-medium">
                            ➖ Remove Drug
                        </button>
                    </div>
                `;

                // Insert the new panel before the extraction section
                const extractionSection = document.getElementById('extraction-section');
                console.log('extractionSection found:', !!extractionSection);

                if (extractionSection) {
                    console.log('Inserting panel before extraction section');
                    dataExtractionTab.insertBefore(newPanel, extractionSection);
                } else {
                    console.log('Appending panel to data extraction tab');
                    dataExtractionTab.appendChild(newPanel);
                }

                console.log('Panel inserted, setting up event listeners...');
                // Add event listeners for the new panel
                setupNewPanelEventListeners(panelId);
                console.log('Event listeners set up for panel:', panelId);
            }

            function setupNewPanelEventListeners(panelId) {
                const drugInput = document.getElementById(`drug-input-${panelId}`);
                const startProjectBtn = document.getElementById(`start-project-btn-${panelId}`);
                const startExtractionBtn = document.getElementById(`start-extraction-btn-${panelId}`);

                // Start project button event listener
                startProjectBtn.addEventListener('click', function() {
                    const drugInputValue = drugInput.value.trim();
                    if (!drugInputValue) {
                        alert('Please enter a drug name or drug class');
                        return;
                    }

                    if (!currentSession || !currentSession.projectName) {
                        alert('Please confirm a project name first');
                        return;
                    }

                    // Disable input and button, grey out the input field
                    drugInput.disabled = true;
                    drugInput.classList.add('bg-gray-100', 'cursor-not-allowed', 'text-gray-500');
                    startProjectBtn.classList.add('hidden');

                    // Detect drug class for this panel
                    detectDrugClassForPanel(drugInputValue, panelId);
                });

                // Start extraction button event listener
                if (startExtractionBtn) {
                    startExtractionBtn.addEventListener('click', function() {
                        try {
                            console.log(`[DEBUG] Start extraction button clicked for panel: ${panelId}`);
                            startBatchExtractionForPanel(panelId);
                        } catch (error) {
                            console.error(`[ERROR] Failed to start extraction for panel ${panelId}:`, error);
                        }
                    });
                }
            }


            function removeDrugSelection() {
                console.log('🔥 FUNCTION START');

                try {
                    // Find all drug selection panels (excluding the original one)
                    const allPanels = document.querySelectorAll('[id^="drug-selection-panel-"]');
                    console.log('Found panels:', allPanels.length);

                    if (allPanels.length > 0) {
                        // Remove the most recently added panel (highest counter number)
                        const lastPanel = allPanels[allPanels.length - 1];
                        console.log('Removing panel:', lastPanel.id);

                        // Remove the panel from DOM
                        lastPanel.remove();

                        console.log('➖ Panel removed successfully');
                        alert('Drug selection panel removed!');
                    } else {
                        console.log('No panels to remove');
                        alert('No additional drug selection panels to remove');
                    }
                } catch (error) {
                    console.error('Error in removeDrugSelection:', error);
                    alert('Error removing panel: ' + error.message);
                }
            }

            function displayDrugClassInfoForPanel(data, panelId) {
                const drugClassInfo = document.getElementById(`drug-class-info-${panelId}`);
                const availableDrugsSection = document.getElementById(`available-drugs-section-${panelId}`);

                console.log('displayDrugClassInfoForPanel received:', data, 'for panel:', panelId);

                if (data.is_single_drug) {
                    drugClassInfo.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span class="text-blue-600 font-medium">💊 Single Drug Detected:</span>
                            <span class="font-semibold">${data.detected_drug}</span>
                            <span class="text-gray-500">(${data.study_count} studies available)</span>
                        </div>
                    `;
                    availableDrugsSection.classList.add('hidden');
                    // Auto-add the single drug to selection
                    addDrugToSelectedListForPanel(data.detected_drug, data.study_count, panelId);
                } else if (data.is_drug_class) {
                    // Handle LLM drug class detection format
                    const drugCount = data.drugs ? data.drugs.length : 0;
                    const drugsWithStudies = data.drugs_with_actual_studies || 0;

                    drugClassInfo.innerHTML = `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <span class="text-green-600 font-medium">🤖 LLM Detected Drug Class:</span>
                                <span class="font-semibold text-green-800">${data.drug_class}</span>
                            </div>
                            <div class="text-sm text-green-700 space-y-1">
                                <p><strong>Description:</strong> ${data.description || 'N/A'}</p>
                                <p><strong>Drugs with clinical trials:</strong> ${drugsWithStudies}</p>

                                ${data.reasoning ? `<p><strong>Reasoning:</strong> ${data.reasoning}</p>` : ''}
                            </div>
                        </div>
                    `;
                    availableDrugsSection.classList.remove('hidden');
                    displayAvailableDrugsForPanel(data.drugs || [], panelId);
                } else {
                    // Fallback for other formats
                    const availableDrugs = data.available_drugs || data.drugs || [];
                    drugClassInfo.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span class="text-blue-600 font-medium">🏷️ Drug Class Detected:</span>
                            <span class="font-semibold">${data.drug_class || 'Unknown'}</span>
                            <span class="text-gray-500">(${availableDrugs.length} drugs available)</span>
                        </div>
                    `;
                    availableDrugsSection.classList.remove('hidden');
                    displayAvailableDrugsForPanel(availableDrugs, panelId);
                }
            }

            // Old addDrugToSelection function removed - now using addDrugToSelectedList

            function removeDrugSelection(drugId) {
                const drugItem = document.getElementById(drugId);
                if (drugItem) {
                    drugItem.remove();
                    selectedDrugs = selectedDrugs.filter(drug => drug.id !== drugId);
                    updateExtractionButton();
                }
            }

            function confirmDrugSelection(drugId) {
                const drugItem = document.getElementById(drugId);
                const drugName = drugItem.querySelector('.drug-name-input').value.trim();

                if (!drugName) {
                    alert('Please enter a drug name');
                    return;
                }

                // Mark as confirmed
                drugItem.classList.add('confirmed');
                drugItem.querySelector('.drug-name-input').disabled = true;
                drugItem.querySelector('.confirm-drug-btn').disabled = true;
                drugItem.querySelector('.remove-drug-btn').style.display = 'none';

                // Add to selected drugs
                selectedDrugs.push({
                    id: drugId,
                    name: drugName,
                    confirmed: true
                });

                updateExtractionButton();
                console.log(`✅ Confirmed drug: ${drugName}`);
            }

            function checkDrugStudyCount(drugName, drugId, callback) {
                if (!drugName.trim()) return;

                // If drugId is provided, update the UI element
                if (drugId) {
                    const drugItem = document.getElementById(drugId);
                    const studyCountDisplay = drugItem.querySelector('.study-count-display');
                    studyCountDisplay.textContent = 'Checking...';
                }

                // Call backend to get study count
                fetch('/api/get-study-count', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ drug_name: drugName })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (drugId) {
                            const drugItem = document.getElementById(drugId);
                            const studyCountDisplay = drugItem.querySelector('.study-count-display');
                            studyCountDisplay.textContent = `${data.study_count} studies available`;
                        }
                        if (callback) {
                            callback(data.study_count);
                        }
                    } else {
                        if (drugId) {
                            const drugItem = document.getElementById(drugId);
                            const studyCountDisplay = drugItem.querySelector('.study-count-display');
                            studyCountDisplay.textContent = 'Error checking studies';
                        }
                        if (callback) {
                            callback(0);
                        }
                    }
                })
                .catch(error => {
                    if (drugId) {
                        const drugItem = document.getElementById(drugId);
                        const studyCountDisplay = drugItem.querySelector('.study-count-display');
                        studyCountDisplay.textContent = 'Error checking studies';
                    }
                    if (callback) {
                        callback(0);
                    }
                });
            }

            function updateExtractionButton() {
                try {
                    const confirmedDrugs = selectedDrugs.filter(drug => drug.confirmed);
                    console.log('[DEBUG] updateExtractionButton - confirmed drugs:', confirmedDrugs.length);

                    // Update original start extraction button (for drugs without panelId)
                    const originalPanelDrugs = confirmedDrugs.filter(drug => !drug.panelId);
                    if (startExtractionBtn) {
                        if (originalPanelDrugs.length > 0) {
                            startExtractionBtn.classList.remove('hidden');
                            console.log('[DEBUG] Showing original start extraction button');
                        } else {
                            startExtractionBtn.classList.add('hidden');
                            console.log('[DEBUG] Hiding original start extraction button');
                        }
                    }

                    // Update start extraction buttons in each panel based on that panel's drugs
                    const allStartExtractionBtns = document.querySelectorAll('[id^="start-extraction-btn-drug-selection-panel-"]');
                    allStartExtractionBtns.forEach(btn => {
                        // Extract panel ID from button ID
                        const panelId = btn.id.replace('start-extraction-btn-', '');
                        const panelDrugs = confirmedDrugs.filter(drug => drug.panelId === panelId);

                        if (panelDrugs.length > 0) {
                            btn.classList.remove('hidden');
                            console.log(`[DEBUG] Showing start extraction button for panel ${panelId} (${panelDrugs.length} drugs)`);
                        } else {
                            btn.classList.add('hidden');
                            console.log(`[DEBUG] Hiding start extraction button for panel ${panelId} (no confirmed drugs)`);
                        }
                    });

                } catch (error) {
                    console.error('[ERROR] Failed to update extraction button:', error);
                    // Fallback UI handling if needed
                }
            }

            function startBatchExtraction() {
                console.log('startBatchExtraction called');
                console.log('selectedDrugs:', selectedDrugs);

                const confirmedDrugs = selectedDrugs.filter(drug => drug.confirmed);
                console.log('confirmedDrugs:', confirmedDrugs);

                if (confirmedDrugs.length === 0) {
                    alert('Please confirm at least one drug selection');
                    return;
                }

                console.log('currentSession:', currentSession);

                if (!currentSession) {
                    alert('No active session. Please start a new session first.');
                    return;
                }

                console.log(`🚀 Starting batch extraction for ${confirmedDrugs.length} drugs...`);

                // Disable all drug checkboxes in the main panel to prevent further selection
                const mainPanelCheckboxes = document.querySelectorAll('.drug-checkbox');
                mainPanelCheckboxes.forEach(checkbox => {
                    checkbox.disabled = true;
                    checkbox.parentElement.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
                    checkbox.parentElement.parentElement.classList.remove('hover:bg-green-100', 'cursor-pointer');
                });



                // Initialize status for confirmed drugs - don't show "Starting extraction...", wait for NCT progress
                confirmedDrugs.forEach(drug => {
                    const drugItem = document.getElementById(drug.id);
                    if (drugItem) {
                        const statusText = drugItem.querySelector('.status-text');
                        if (statusText) {
                            statusText.textContent = '';
                            statusText.className = 'status-text text-sm text-blue-600';
                        }
                    }
                });

                // Start sequential extraction process (one drug at a time)
                console.log('Starting sequential extraction...');
                startSequentialExtraction(confirmedDrugs, 0);
            }

            function startBatchExtractionForPanel(panelId) {
                console.log(`startBatchExtractionForPanel called for panel: ${panelId}`);

                // Filter drugs that belong to this specific panel
                const panelDrugs = selectedDrugs.filter(drug => drug.confirmed && drug.panelId === panelId);
                console.log(`Panel ${panelId} confirmed drugs:`, panelDrugs);

                if (panelDrugs.length === 0) {
                    alert('Please confirm at least one drug selection in this panel');
                    return;
                }

                if (!currentSession) {
                    alert('No active session. Please start a new session first.');
                    return;
                }

                console.log(`🚀 Starting extraction for ${panelDrugs.length} drugs from panel ${panelId}...`);

                // Disable all drug checkboxes in this specific panel to prevent further selection
                const panelCheckboxes = document.querySelectorAll(`.drug-checkbox-${panelId}`);
                panelCheckboxes.forEach(checkbox => {
                    checkbox.disabled = true;
                    checkbox.parentElement.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
                    checkbox.parentElement.parentElement.classList.remove('hover:bg-green-100', 'cursor-pointer');
                });



                // Initialize status for panel drugs - don't show "Starting extraction...", wait for NCT progress
                panelDrugs.forEach(drug => {
                    const drugItem = document.getElementById(drug.id);
                    if (drugItem) {
                        const statusText = drugItem.querySelector('.status-text');
                        if (statusText) {
                            statusText.textContent = '';
                            statusText.className = 'status-text text-sm text-blue-600';
                        }
                    }
                });

                // Start sequential extraction process for panel drugs (one drug at a time)
                console.log(`Starting sequential extraction for panel ${panelId}...`);
                startSequentialExtraction(panelDrugs, 0);
            }

            function startSequentialExtraction(drugsList, currentIndex) {
                if (currentIndex >= drugsList.length) {
                    console.log('✅ All drugs extraction completed!');
                    return;
                }

                const currentDrug = drugsList[currentIndex];
                const drugName = currentDrug.genericName || currentDrug.name;

                console.log(`🔄 Processing drug ${currentIndex + 1}/${drugsList.length}: ${drugName}`);

                // Get drug item for status updates (don't set initial message, wait for NCT progress)
                const drugItem = document.getElementById(currentDrug.id);

                // Extract single drug using batch-extract endpoint with one drug
                fetch('/api/batch-extract', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        project_name: currentSession.projectName,
                        drugs: [drugName]  // Send as array with single drug
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log(`Drug ${drugName} extraction response:`, data);

                    if (data.success) {
                        console.log(`✅ ${drugName}: ${data.message}`);
                        // Start monitoring this drug's extraction progress
                        monitorSingleDrugExtraction(currentDrug, drugsList, currentIndex);
                    } else {
                        console.log(`❌ ${drugName} Error: ${data.message}`);
                        // Update status for current drug
                        if (drugItem) {
                            const statusText = drugItem.querySelector('.status-text');
                            if (statusText) {
                                statusText.textContent = `❌ Failed: ${data.message}`;
                                statusText.className = 'status-text text-sm text-red-600';
                            }
                        }

                        // Continue with next drug
                        setTimeout(() => {
                            startSequentialExtraction(drugsList, currentIndex + 1);
                        }, 500);
                    }
                })
                .catch(error => {
                    console.error(`Drug ${drugName} extraction error:`, error);

                    // Update status for current drug
                    if (drugItem) {
                        const statusText = drugItem.querySelector('.status-text');
                        if (statusText) {
                            statusText.textContent = `❌ Error: ${error.message}`;
                            statusText.className = 'status-text text-sm text-red-600';
                        }
                    }

                    // Continue with next drug even if current one failed
                    setTimeout(() => {
                        startSequentialExtraction(drugsList, currentIndex + 1);
                    }, 500);
                });
            }

            function monitorSingleDrugExtraction(currentDrug, drugsList, currentIndex) {
                const drugName = currentDrug.genericName || currentDrug.name;
                const drugItem = document.getElementById(currentDrug.id);
                let localLastDisplayedIndex = 0; // Use local variable to avoid conflict
                let drugStartTime = Date.now(); // Track when this drug started

                console.log(`📊 Monitoring extraction progress for ${drugName}...`);

                const checkProgress = () => {
                    fetch('/api/progress')
                        .then(response => response.json())
                        .then(data => {

                            if (data.terminal_output && drugItem) {
                                const statusText = drugItem.querySelector('.status-text');
                                if (!statusText) return;

                                // Get new messages since last update
                                const newMessages = data.terminal_output.slice(localLastDisplayedIndex);
                                localLastDisplayedIndex = data.terminal_output.length;

                                // Filter messages that are relevant to current drug
                                const relevantMessages = newMessages.filter(message => {
                                    // Include messages that mention the current drug name
                                    const mentionsDrug = message.toLowerCase().includes(drugName.toLowerCase());
                                    // Include NCT progress messages only if they appear after this drug started
                                    const hasNCT = /NCT\d+/.test(message);
                                    // Include general completion/error messages that are recent
                                    const isGeneral = message.includes('Extraction completed') ||
                                                    message.includes('✅ Extraction completed') ||
                                                    message.includes('All studies extracted') ||
                                                    message.includes('❌');

                                    // For NCT messages, only include them if we're currently processing this drug
                                    if (hasNCT && !mentionsDrug) {
                                        // Only include NCT messages if no other drug is mentioned in recent messages
                                        return true; // Assume it's for current drug during active extraction
                                    }

                                    return mentionsDrug || isGeneral;
                                });

                                // Look for NCT numbers and progress in the relevant messages
                                let latestProgress = null;
                                let isCompleted = false;
                                let hasError = false;

                                relevantMessages.forEach(message => {
                                    // Look for NCT progress patterns like "NCT01054300 (1/19)"
                                    const nctMatch = message.match(/NCT\d+\s*\((\d+)\/(\d+)\)/);
                                    if (nctMatch) {
                                        const nctNumber = message.match(/NCT\d+/)[0];
                                        const current = nctMatch[1];
                                        const total = nctMatch[2];
                                        latestProgress = `${nctNumber} (${current}/${total})`;
                                    }

                                    // Check for completion indicators - match server completion messages
                                    if (message.includes('Extraction completed for') || message.includes('✅ Extraction completed') ||
                                        message.includes('Extraction finished') || message.includes('All studies extracted') ||
                                        data.status === 'completed') {
                                        isCompleted = true;
                                        console.log(`🎯 Completion detected for ${drugName}: "${message}" or status: ${data.status}`);
                                    }

                                    // Check for error indicators
                                    if (message.includes('error') || message.includes('failed') ||
                                        message.includes('❌') || message.includes('Error:')) {
                                        hasError = true;
                                    }
                                });

                                // Update status based on what we found
                                if (isCompleted) {
                                    console.log(`✅ Drug ${drugName} completed, moving to next drug (${currentIndex + 1})`);
                                    statusText.textContent = `✅ Extraction completed`;
                                    statusText.className = 'status-text text-sm text-green-600';

                                    // Move to next drug
                                    setTimeout(() => {
                                        startSequentialExtraction(drugsList, currentIndex + 1);
                                    }, 500);
                                    return;
                                } else if (hasError) {
                                    statusText.textContent = `❌ Extraction failed`;
                                    statusText.className = 'status-text text-sm text-red-600';

                                    // Move to next drug even if failed
                                    setTimeout(() => {
                                        startSequentialExtraction(drugsList, currentIndex + 1);
                                    }, 500);
                                    return;
                                } else if (latestProgress) {
                                    // Show the latest NCT progress
                                    statusText.textContent = latestProgress;
                                    statusText.className = 'status-text text-sm text-blue-600';
                                }
                                // Remove the default "Starting extraction..." message - just wait for NCT progress
                            }

                            // Continue monitoring if not completed
                            setTimeout(checkProgress, 100); // Check every 100ms for fast updates
                        })
                        .catch(error => {
                            console.error(`Error monitoring ${drugName}:`, error);

                            // Update status and move to next drug
                            if (drugItem) {
                                const statusText = drugItem.querySelector('.status-text');
                                if (statusText) {
                                    statusText.textContent = `❌ Monitoring error`;
                                    statusText.className = 'status-text text-sm text-red-600';
                                }
                            }

                            setTimeout(() => {
                                startSequentialExtraction(drugsList, currentIndex + 1);
                            }, 500);
                        });
                };

                // Start monitoring
                checkProgress();
            }

            function monitorBatchExtraction() {
                console.log('Starting batch extraction monitoring...');

                const checkProgress = () => {
                    fetch('/api/progress')
                        .then(response => response.json())
                        .then(data => {
                            console.log('Progress data:', data);
                            updateBatchExtractionProgress(data);
                            // Terminal output removed

                            if (data.status === 'running' && data.operation === 'batch_extract') {
                                setTimeout(checkProgress, 100);
                            } else if (data.status === 'completed' && data.operation === 'batch_extract') {
                                console.log('🎉 Batch extraction completed!');
                                // Reset extraction button state
                                startExtractionBtn.classList.add('hidden');
                            } else if (data.status === 'error' || data.status === 'stopped') {
                                console.log(`❌ ${data.message}`);
                            }
                        })
                        .catch(error => {
                            console.error('Progress check failed:', error);
                            console.log(`❌ Progress check failed: ${error}`);
                        });
                };
                checkProgress();
            }

            // Keep track of last displayed message index to show sequential updates
            // (using global lastDisplayedIndex declared at top)

            function updateBatchExtractionProgress(data) {
                if (data.operation === 'batch_extract') {
                    // Update drug status text based on terminal output
                    if (data.terminal_output && data.terminal_output.length > 0) {
                        // Get new messages since last update
                        const newMessages = data.terminal_output.slice(lastDisplayedIndex);

                        // Update each confirmed drug's status
                        selectedDrugs.forEach(drug => {
                            if (drug.confirmed) {
                                const drugItem = document.getElementById(drug.id);
                                if (drugItem) {
                                    const statusText = drugItem.querySelector('.status-text');
                                    if (statusText) {
                                        // Find relevant messages for this drug
                                        const drugName = drug.genericName || drug.name;
                                        let latestStatus = statusText.textContent; // Keep current status

                                        // Look for the most recent relevant message
                                        for (let i = data.terminal_output.length - 1; i >= 0; i--) {
                                            const msg = data.terminal_output[i];
                                            if (msg.includes(drugName)) {
                                                if (msg.includes('Completed')) {
                                                    latestStatus = '✅ Completed';
                                                    break;
                                                } else if (msg.includes('studies available')) {
                                                    const match = msg.match(/(\d+) studies available/);
                                                    if (match) {
                                                        latestStatus = `${match[1]} studies available`;
                                                    }
                                                } else if (msg.includes('Processing')) {
                                                    latestStatus = 'Processing...';
                                                }
                                            } else if (msg.includes('NCT') && msg.includes('/')) {
                                                // Show current NCT being processed
                                                latestStatus = msg.trim();
                                                break; // Use the most recent NCT message
                                            }
                                        }

                                        statusText.textContent = latestStatus;
                                    }
                                }
                            }
                        });

                        // Update the last displayed index
                        lastDisplayedIndex = data.terminal_output.length;
                    }
                }
            }

            function loadModels() {
                console.log('Loading models...');
                llmStatus.textContent = 'Loading models...';

                // First, show fallback models immediately
                llmSelect.innerHTML = `
                    <option value="">Select AI Model</option>
                    <option value="qwen2.5:latest">qwen2.5:latest (4.4 GB)</option>
                `;
                llmStatus.textContent = '✅ Models loaded';
                llmStatus.className = 'text-xs text-green-600 mt-1';

                // Try to get models from API
                fetch('/api/models')
                    .then(response => {
                        console.log('API response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('API data received:', data);
                        if (data.success && data.models) {
                            llmSelect.innerHTML = '<option value="">Select AI Model</option>';

                            data.models.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.name;
                                option.textContent = model.display;
                                llmSelect.appendChild(option);
                            });

                            llmStatus.textContent = `✅ Found ${data.models.length} Ollama models`;
                            llmStatus.className = 'text-xs text-green-600 mt-1';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading models:', error);
                        llmStatus.textContent = '⚠️ Using fallback models';
                        llmStatus.className = 'text-xs text-yellow-600 mt-1';
                    });
            }

            function loadDrugFolders() {
                console.log('Loading drug folders...');
                drugFolderStatus.textContent = 'Loading available drugs...';

                fetch('/api/drug-folders')
                    .then(response => {
                        console.log('Drug folders API response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Drug folders data received:', data);
                        if (data.folders && data.folders.length > 0) {
                            drugFolderSelect.innerHTML = '<option value="">Select Drug to Analyze</option>';
                            data.folders.forEach(folder => {
                                const option = document.createElement('option');
                                option.value = folder.name;
                                option.textContent = folder.display_name || `${folder.name} (${folder.study_count} studies)`;
                                drugFolderSelect.appendChild(option);
                            });
                            drugFolderStatus.textContent = `Found ${data.folders.length} drug datasets`;
                        } else {
                            drugFolderSelect.innerHTML = '<option value="">No extracted data found</option>';
                            drugFolderStatus.textContent = 'No extracted data found - run extraction first';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading drug folders:', error);
                        drugFolderStatus.textContent = 'Error loading drug folders';
                    });
            }

            // Old extraction handlers removed - using new batch extraction system

            // Stop analysis button handler
            // Stop analysis button removed - using progressive filtering instead

            function monitorExtractionProgress() {
                const checkProgress = () => {
                    fetch('/api/progress')
                        .then(response => response.json())
                        .then(data => {
                            updateExtractionProgress(data);
                            updateTerminalOutput(data);

                            if (data.status === 'running' && data.operation === 'extract') {
                                setTimeout(checkProgress, 200);
                            } else if (data.status === 'completed' && data.operation === 'extract') {
                                resetExtractionButtons();
                                enableAnalysis(data);
                            } else if (data.status === 'error' || data.status === 'stopped') {
                                console.log(`❌ ${data.message}`);
                                resetExtractionButtons();
                            }
                        })
                        .catch(error => {
                            console.log(`❌ Progress check failed: ${error}`);
                        });
                };
                checkProgress();
            }

            function monitorAnalysisProgress() {
                const checkProgress = () => {
                    fetch('/api/progress')
                        .then(response => response.json())
                        .then(data => {
                            updateAnalysisProgress(data);
                            updateTerminalOutput(data);

                            if (data.status === 'running' && data.operation === 'analyze') {
                                setTimeout(checkProgress, 200);
                            } else if (data.status === 'completed' && data.operation === 'analyze') {
                                showFinalResults(data);
                            } else if (data.status === 'error' || data.status === 'stopped') {
                                console.log(`❌ ${data.message}`);
                            }
                        })
                        .catch(error => {
                            console.log(`❌ Progress check failed: ${error}`);
                        });
                };
                checkProgress();
            }

            function updateExtractionProgress(data) {
                if (data.operation === 'extract') {
                    document.getElementById('extract-bar').style.width = `${data.progress}%`;
                    document.getElementById('extract-percent').textContent = `${Math.floor(data.progress)}%`;
                    document.getElementById('extract-text').textContent = data.message;
                }
            }

            function updateAnalysisProgress(data) {
                if (data.operation === 'analyze') {
                    document.getElementById('analyze-bar').style.width = `${data.progress}%`;
                    document.getElementById('analyze-percent').textContent = `${Math.floor(data.progress)}%`;
                    document.getElementById('analyze-text').textContent = data.message;
                }
            }

            function enableAnalysis(data) {
                // Load drug folders to refresh the dropdown
                loadDrugFolders();

                const studyCount = data.study_count || 'Unknown';
                const drugName = data.drug_name || 'Unknown';
                studiesStatus.textContent = `✅ ${studyCount} studies extracted for ${drugName} - Select from dropdown to analyze`;
                studiesStatus.className = 'text-xs text-green-600';
            }

            function updateTerminalOutput(data) {
                // Terminal output removed - using console.log instead
                if (data.terminal_output && data.terminal_output.length > lastOutputLength) {
                    for (let i = lastOutputLength; i < data.terminal_output.length; i++) {
                        console.log(data.terminal_output[i]);
                    }
                    lastOutputLength = data.terminal_output.length;
                }
            }

            function showFinalResults(data) {
                if (data.results && data.results.length > 0) {
                    addTerminalLine('📊 MATCHING STUDIES:', 'terminal-warning');
                    data.results.forEach((study, index) => {
                        const confidence = study.analysis?.confidence || 0;
                        const title = study.study_title || 'Unknown title';
                        addTerminalLine(`${index + 1}. ${study.nct_id} (confidence: ${confidence.toFixed(2)})`, 'terminal-response');
                        addTerminalLine(`   ${title}`, 'terminal-response');
                    });
                } else {
                    addTerminalLine('No studies matched the search criteria.', 'terminal-warning');
                }
            }

            function resetExtractionButtons() {
                extractBtn.classList.remove('hidden');
                stopExtractBtn.classList.add('hidden');
            }

            // Analysis buttons removed - using progressive filtering instead







            // Progressive Filtering Functions for Meta-Analysis Tab
            function discoverTermsMeta() {
                const category = document.getElementById('outcome-section-select-meta').value;
                const searchTerm = document.getElementById('outcome-term-meta').value.trim();
                const discoverBtn = document.getElementById('discover-terms-btn-meta');
                const discoverStatus = document.getElementById('discover-status-meta');

                if (!drugFolderSelect.value) {
                    alert('Please select a drug folder first');
                    return;
                }

                const studiesDir = `output/${drugFolderSelect.value}`;

                if (!category || !searchTerm) {
                    alert('Please select a category and enter a search term');
                    return;
                }

                // Hide button and show status message
                discoverBtn.classList.add('hidden');
                discoverStatus.classList.remove('hidden');

                // Get AI model configuration for enhanced term discovery
                const aiConfig = getAiModelConfiguration();

                fetch('/api/enhanced-discover-terms', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        studies_dir: studiesDir,
                        category: category,
                        search_term: searchTerm,
                        ai_model: aiConfig.model || 'gpt-4'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTermSelectionMeta(data.results);
                    } else {
                        alert('Error discovering terms: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error);
                })
                .finally(() => {
                    // Show button again and hide status message
                    discoverBtn.classList.remove('hidden');
                    discoverStatus.classList.add('hidden');
                });
            }

            function displayTermSelectionMeta(results) {
                const termCheckboxes = document.getElementById('term-checkboxes-meta');
                const termSelection = document.getElementById('term-selection-meta');
                termCheckboxes.innerHTML = '';

                if (results.related_terms.length === 0) {
                    termCheckboxes.innerHTML = '<div class="text-gray-500 col-span-full text-center">No related terms found</div>';
                    termSelection.classList.remove('hidden');
                    return;
                }

                // Add the original search term as first option
                const originalDiv = document.createElement('div');
                originalDiv.className = 'flex items-center space-x-2';
                const studiesText = results.studies_found === 1 ? 'study' : 'studies';
                originalDiv.innerHTML = `
                    <input type="checkbox" id="term-${results.search_term}" value="${results.search_term}" class="rounded" checked>
                    <label for="term-${results.search_term}" class="text-sm cursor-pointer font-medium">
                        ${results.search_term} <span class="text-gray-500">(${results.studies_found} ${studiesText})</span>
                    </label>
                `;
                termCheckboxes.appendChild(originalDiv);

                // Add related terms (server provides pre-formatted strings with study counts)
                results.related_terms.forEach(termWithCount => {
                    // Extract just the term name for the ID (before the parentheses)
                    const termValue = termWithCount.split(' (')[0];
                    const div = document.createElement('div');
                    div.className = 'flex items-center space-x-2';
                    div.innerHTML = `
                        <input type="checkbox" id="term-${termValue}" value="${termWithCount}" class="rounded">
                        <label for="term-${termValue}" class="text-sm cursor-pointer">
                            ${termWithCount}
                        </label>
                    `;
                    termCheckboxes.appendChild(div);
                });

                termSelection.classList.remove('hidden');
            }

            function confirmOutcomeMeta() {
                const selectedTerms = Array.from(document.querySelectorAll('#term-checkboxes-meta input:checked'))
                    .map(cb => cb.value);

                if (selectedTerms.length === 0) {
                    alert('Please select at least one term');
                    return;
                }

                const section = document.getElementById('outcome-section-select-meta').value;
                const searchTerm = document.getElementById('outcome-term-meta').value.trim();

                // Add outcome criteria
                const outcomeCriteria = {
                    id: 'outcome', // Fixed ID for outcome
                    section: section,
                    terms: selectedTerms,
                    type: 'outcome',
                    original_term: searchTerm
                };

                currentCriteria = [outcomeCriteria]; // Reset and add outcome

                // Grey out and disable the outcome section elements
                const sectionSelect = document.getElementById('outcome-section-select-meta');
                const searchTermInput = document.getElementById('outcome-term-meta');
                const discoverBtn = document.getElementById('discover-terms-btn-meta');
                const confirmBtn = document.getElementById('confirm-outcome-btn-meta');
                const termCheckboxes = document.querySelectorAll('#term-checkboxes-meta input');

                // Disable and grey out form elements
                sectionSelect.disabled = true;
                sectionSelect.classList.add('bg-gray-100', 'cursor-not-allowed', 'text-gray-500');

                searchTermInput.disabled = true;
                searchTermInput.classList.add('bg-gray-100', 'cursor-not-allowed', 'text-gray-500', 'opacity-75');

                discoverBtn.disabled = true;
                discoverBtn.classList.add('opacity-50', 'cursor-not-allowed');

                confirmBtn.disabled = true;
                confirmBtn.classList.add('opacity-50', 'cursor-not-allowed');

                // Disable all term checkboxes and grey out their containers
                termCheckboxes.forEach(checkbox => {
                    checkbox.disabled = true;
                    checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
                });

                // Apply filtering
                applyProgressiveFilteringMeta();

                // Keep term selection visible - Additional criteria section will only show when "Add Criteria" is clicked
                // document.getElementById('term-selection-meta').classList.add('hidden'); // Commented out to keep terms visible

                // Note: updateFilterSummaryMeta() will be called by applyProgressiveFilteringMeta() when API response is received
            }



            function addCriteria() {
                const criteriaList = document.getElementById('criteria-list');
                const criteriaNumber = criteriaIdCounter++;
                const criteriaId = `criteria-${criteriaNumber}`;

                const criteriaDiv = document.createElement('div');
                criteriaDiv.id = `criteria-item-${criteriaId}`;
                criteriaDiv.className = 'border border-gray-200 rounded-md p-3 mb-3';
                criteriaDiv.setAttribute('data-criteria-id', criteriaId);
                criteriaDiv.innerHTML = `
                    <div class="criteria-header flex items-center justify-between mb-2">
                        <span class="text-sm font-medium">Criteria ${criteriaNumber}</span>
                        <button class="remove-criteria-btn px-3 py-1 bg-red-100 hover:bg-red-200 text-red-600 hover:text-red-800 rounded-md text-sm font-medium flex items-center gap-1 transition-all" data-criteria-id="${criteriaId}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd" />
                            </svg>
                            ×
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <select class="criteria-section w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="">Select module...</option>
                            <optgroup label="Protocol Section">
                                <option value="protocolSection_identificationModule">Identification Module</option>
                                <option value="protocolSection_statusModule">Status Module</option>
                                <option value="protocolSection_oversightModule">Oversight Module</option>
                                <option value="protocolSection_descriptionModule">Description Module</option>
                                <option value="protocolSection_conditionsModule">Conditions Module</option>
                                <option value="protocolSection_designModule">Design Module</option>
                                <option value="protocolSection_armsInterventionsModule">Arms Interventions Module</option>
                                <option value="protocolSection_outcomesModule">Outcomes Module</option>
                                <option value="protocolSection_eligibilityModule">Eligibility Module</option>
                                <option value="protocolSection_contactsLocationsModule">Contacts Locations Module</option>
                                <option value="protocolSection_referencesModule">References Module</option>
                                <option value="protocolSection_ipdSharingStatementModule">IPD Sharing Statement Module</option>
                                <option value="protocolSection_sponsorCollaboratorsModule">Sponsor Collaborators Module</option>
                                <option value="protocolSection_largeDocumentModule">Large Document Module</option>
                            </optgroup>
                            <optgroup label="Results Section">
                                <option value="resultsSection_participantFlowModule">Participant Flow Module</option>
                                <option value="resultsSection_baselineCharacteristicsModule">Baseline Characteristics Module</option>
                                <option value="resultsSection_outcomeMeasuresModule">Outcome Measures Module</option>
                                <option value="resultsSection_adverseEventsModule">Adverse Events Module</option>
                            </optgroup>
                        </select>
                        <input type="text" class="criteria-term w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Search term">
                        <button class="discover-criteria-btn px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" data-criteria-id="${criteriaId}">
                            Discover Terms
                        </button>
                    </div>
                    <div class="criteria-terms mt-3 hidden">
                        <!-- Terms will be populated here -->
                    </div>
                `;

                criteriaList.appendChild(criteriaDiv);

                // Add event listeners for the new buttons
                criteriaDiv.querySelector('.remove-criteria-btn').addEventListener('click', function() {
                    removeCriteria(this.getAttribute('data-criteria-id'));
                });

                criteriaDiv.querySelector('.discover-criteria-btn').addEventListener('click', function() {
                    discoverCriteriaTerms(this.getAttribute('data-criteria-id'));
                });
            }

            function removeCriteria(criteriaId) {
                // Remove the criteria element from the DOM
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                if (criteriaElement) {
                    criteriaElement.remove();
                }

                // Remove the criteria from the array by ID
                currentCriteria = currentCriteria.filter(criteria => criteria.id !== criteriaId);

                // Re-apply filtering with the updated criteria
                applyProgressiveFiltering();
            }

            function resetAllCriteria() {
                // Confirm with user before resetting
                if (!confirm('Are you sure you want to reset all additional criteria? This will remove all secondary filters and keep only the primary outcome filter.')) {
                    return;
                }

                // Remove all additional criteria from DOM
                const criteriaList = document.getElementById('criteria-list');
                criteriaList.innerHTML = '';

                // Reset criteria array to only include outcome (first item)
                currentCriteria = currentCriteria.filter(c => c.type === 'outcome');

                // Reset criteria counter
                criteriaIdCounter = 1;

                // Add terminal feedback
                addTerminalLine('🔄 All additional criteria reset - back to primary filter only', 'terminal-response');

                // Re-apply filtering with only the outcome criteria
                applyProgressiveFiltering();
            }

            function discoverCriteriaTerms(criteriaId) {
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                const section = criteriaElement.querySelector('.criteria-section').value;
                const searchTerm = criteriaElement.querySelector('.criteria-term').value.trim();
                const discoverBtn = criteriaElement.querySelector('.discover-criteria-btn');
                const studiesDir = `output/${drugFolderSelect.value}`;

                console.log('Discovering criteria terms:');
                console.log('Section:', section);
                console.log('Search term:', searchTerm);
                console.log('Filtered studies:', filteredStudies);
                console.log('Filtered studies count:', filteredStudies ? filteredStudies.length : 'null');

                if (!section || !searchTerm) {
                    alert('Please select a section and enter a search term');
                    return;
                }

                if (!filteredStudies || filteredStudies.length === 0) {
                    alert('Please complete the Outcome (Primary Filter) first by selecting terms and clicking "Confirm & Apply Filter".');
                    addTerminalLine('❌ No filtered studies available - complete outcome filter first', 'terminal-error');
                    return;
                }

                discoverBtn.disabled = true;
                discoverBtn.textContent = 'Discovering...';

                // Add terminal feedback
                addTerminalLine(`🔍 Discovering terms for "${searchTerm}" in ${section}...`, 'terminal-response');
                addTerminalLine(`📊 Searching within ${filteredStudies.length} filtered studies`, 'terminal-response');

                // Get AI model configuration for enhanced term discovery
                const aiConfig = getAiModelConfiguration();

                fetch('/api/discover-terms', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        studies_dir: studiesDir,
                        section: section,
                        search_term: searchTerm,
                        filtered_studies: filteredStudies,  // Use filtered studies from primary filter
                        ai_config: aiConfig.model ? { use_ai: true, model: aiConfig.model } : { use_ai: false }
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const termCount = data.results.related_terms ? data.results.related_terms.length : 0;
                        addTerminalLine(`✅ Found ${termCount} related terms`, 'terminal-response');
                        displayCriteriaTermSelection(criteriaId, data.results);
                    } else {
                        addTerminalLine(`❌ Error discovering terms: ${data.error}`, 'terminal-error');
                        alert('Error discovering terms: ' + data.error);
                    }
                })
                .catch(error => {
                    addTerminalLine(`❌ Error: ${error}`, 'terminal-error');
                    alert('Error: ' + error);
                })
                .finally(() => {
                    discoverBtn.disabled = false;
                    discoverBtn.textContent = 'Discover Terms';
                });
            }

            function displayCriteriaTermSelection(criteriaId, results) {
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                const termsContainer = criteriaElement.querySelector('.criteria-terms');

                termsContainer.innerHTML = '';

                if (results.related_terms.length === 0) {
                    termsContainer.innerHTML = '<div class="text-gray-500 text-center">No related terms found</div>';
                    termsContainer.classList.remove('hidden');
                    return;
                }

                const termsDiv = document.createElement('div');
                termsDiv.innerHTML = `
                    <label class="block text-sm font-medium text-gray-700 mb-2">Select Acceptable Terms:</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
                        ${results.related_terms.map(termWithCount => {
                            // Extract just the term name for the value (before the parentheses)
                            const termValue = termWithCount.split(' (')[0];
                            return `
                                <div class="flex items-center space-x-2">
                                    <input type="checkbox" id="criteria-term-${criteriaId}-${termValue}" value="${termValue}" class="rounded">
                                    <label for="criteria-term-${criteriaId}-${termValue}" class="text-sm cursor-pointer">
                                        ${termWithCount}
                                    </label>
                                </div>
                            `;
                        }).join('')}
                    </div>
                    <div class="mt-3 flex gap-2">
                        <button class="confirm-criteria-btn px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700" data-criteria-id="${criteriaId}">
                            Confirm & Apply Filter
                        </button>
                        <button class="cancel-criteria-btn px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600" data-criteria-id="${criteriaId}">
                            Cancel
                        </button>
                    </div>
                `;

                termsContainer.appendChild(termsDiv);
                termsContainer.classList.remove('hidden');

                // Add event listeners for confirm/cancel buttons
                termsDiv.querySelector('.confirm-criteria-btn').addEventListener('click', function() {
                    confirmCriteria(this.getAttribute('data-criteria-id'));
                });


            }

            function confirmCriteria(criteriaId) {
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                const selectedTerms = Array.from(criteriaElement.querySelectorAll('.criteria-terms input:checked'))
                    .map(cb => cb.value);

                if (selectedTerms.length === 0) {
                    alert('Please select at least one term');
                    return;
                }

                const section = criteriaElement.querySelector('.criteria-section').value;
                const searchTerm = criteriaElement.querySelector('.criteria-term').value.trim();

                // Add or update criteria
                const criteria = {
                    id: criteriaId,
                    section: section,
                    selected_terms: selectedTerms,
                    type: 'additional',
                    original_term: searchTerm
                };

                // Add new criteria (no editing allowed - criteria are read-only after confirmation)
                currentCriteria.push(criteria);

                // Add terminal feedback
                addTerminalLine(`🎯 Applying criteria filter: ${section} - ${selectedTerms.join(', ')}`, 'terminal-response');

                // Keep the term selection interface visible but make it read-only
                const termsElement = criteriaElement.querySelector('.criteria-terms');
                if (termsElement) {
                    // termsElement.classList.add('hidden'); // Commented out to keep terms visible
                    // Make checkboxes read-only but keep them visible
                    const checkboxes = termsElement.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(cb => cb.disabled = true);

                    // Hide the confirm button since criteria is confirmed
                    const buttons = termsElement.querySelectorAll('.confirm-criteria-btn');
                    buttons.forEach(btn => btn.style.display = 'none');
                }

                // Slightly grey out the criteria to show it's confirmed but keep it readable
                criteriaElement.classList.add('bg-gray-50');
                criteriaElement.style.opacity = '0.8';

                // Keep the criteria header as is - no status text changes

                // Apply progressive filtering
                applyProgressiveFiltering();
            }

            function cancelCriteria(criteriaId) {
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                criteriaElement.querySelector('.criteria-terms').classList.add('hidden');
            }

            function applyProgressiveFiltering() {
                if (!drugFolderSelect.value) {
                    alert('Please select a drug folder first');
                    return;
                }

                const studiesDir = `output/${drugFolderSelect.value}`;

                fetch('/api/filter-studies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        studies_dir: studiesDir,
                        criteria_list: currentCriteria
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        filteredStudies = data.results.filtered_studies;
                        addTerminalLine(`📊 Filter applied: ${filteredStudies.length} studies remaining`, 'terminal-response');
                        updateFilterSummary(data.results.filter_steps);
                    } else {
                        addTerminalLine(`❌ Error filtering studies: ${data.error}`, 'terminal-error');
                        alert('Error filtering studies: ' + data.error);
                    }
                })
                .catch(error => {
                    addTerminalLine(`❌ Error: ${error}`, 'terminal-error');
                    alert('Error: ' + error);
                });
            }

            function updateFilterSummary(filterSteps = null) {
                if (!filterSteps) return;

                const filterStepsDiv = document.getElementById('filter-steps');
                filterStepsDiv.innerHTML = '';

                filterSteps.forEach(step => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'mb-2';

                    // Use step_name from backend if available, otherwise determine from step number
                    let stepName = step.step_name || (step.step === 1 ? 'Primary Outcome' : `Criteria ${step.step - 1}`);

                    stepDiv.innerHTML = `
                        <div class="font-medium">${stepName}: ${step.count} studies</div>
                        ${step.criteria ? `<div class="text-xs text-gray-600">Section: ${step.criteria.section}, Terms: ${step.criteria.selected_terms.join(', ')}</div>` : ''}
                    `;
                    filterStepsDiv.appendChild(stepDiv);
                });

                filterSummary.classList.remove('hidden');

                // Progressive filtering complete - ready for additional criteria or final analysis
            }

            // Meta-Analysis Tab Functions
            function applyProgressiveFilteringMeta() {
                if (!drugFolderSelect.value) {
                    alert('Please select a drug folder first');
                    return;
                }

                const studiesDir = `output/${drugFolderSelect.value}`;

                console.log('🔍 Applying enhanced progressive filtering:');  // Debug log
                console.log('   - Studies dir:', studiesDir);  // Debug log
                console.log('   - Current criteria:', currentCriteria);  // Debug log

                fetch('/api/enhanced-filter-studies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        studies_dir: studiesDir,
                        criteria_list: currentCriteria
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        filteredStudies = data.results.filtered_studies;
                        console.log(`📊 Enhanced filter applied: ${filteredStudies.length} studies remaining`);
                        updateFilterSummaryMeta(data.results.filter_steps);
                    } else {
                        console.log(`❌ Error filtering studies: ${data.error}`);
                        alert('Error filtering studies: ' + data.error);
                    }
                })
                .catch(error => {
                    console.log(`❌ Error: ${error}`);
                    alert('Error: ' + error);
                });
            }

            function updateFilterSummaryMeta(filterSteps = null) {
                if (!filterSteps) return;

                const filterStepsDiv = document.getElementById('filter-steps-meta');
                const filterSummary = document.getElementById('filter-summary-meta');
                filterStepsDiv.innerHTML = '';

                filterSteps.forEach(step => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'mb-3 p-3 bg-gray-50 rounded-md border';

                    // Use step_name from backend if available, otherwise determine from step number
                    let stepName = step.step_name || (step.step === 1 ? 'Primary Outcome' : `Criteria ${step.step - 1}`);

                    // Create the main step info
                    let stepContent = `
                        <div class="font-medium text-gray-800 mb-2">
                            ${stepName}: ${step.studies_remaining} unique studies remaining
                        </div>
                    `;

                    // Add criteria details if available
                    if (step.criteria) {
                        const sectionName = step.criteria.section.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());

                        stepContent += `
                            <div class="text-sm text-gray-600 mb-2">
                                <strong>Section:</strong> ${sectionName}
                            </div>
                        `;

                        // Add terms with their individual counts
                        if (step.criteria.terms && step.criteria.terms.length > 0) {
                            stepContent += `
                                <div class="text-sm text-gray-600 mb-2">
                                    <strong>Selected Terms:</strong> ${step.criteria.terms.join(', ')}
                                </div>
                            `;
                        }

                        // Add overlap explanation when there are overlaps
                        if (step.term_breakdown) {
                            const totalIndividual = Object.values(step.term_breakdown).reduce((sum, count) => sum + count, 0);
                            if (totalIndividual > step.studies_remaining) {
                                stepContent += `
                                    <div class="text-xs text-blue-600 mt-2 italic">
                                        Note: Individual term counts sum to ${totalIndividual}, but ${step.studies_remaining} unique studies remain after removing overlaps.
                                    </div>
                                `;
                            }
                        }
                    }

                    stepDiv.innerHTML = stepContent;
                    filterStepsDiv.appendChild(stepDiv);
                });

                filterSummary.classList.remove('hidden');

                // Update status and show confirm button
                const studiesStatus = document.getElementById('studies-status-meta');
                const finalCount = filterSteps[filterSteps.length - 1]?.studies_remaining || 0;
                studiesStatus.textContent = `✅ ${finalCount} studies filtered and ready`;
                studiesStatus.className = 'text-xs text-green-600';

                // Show the action buttons row
                const actionButtons = document.getElementById('filter-action-buttons');
                actionButtons.classList.remove('hidden');
            }

            function addCriteriaMeta() {
                const criteriaList = document.getElementById('criteria-list-meta');
                const criteriaNumber = criteriaIdCounter++;
                const criteriaId = `criteria-${criteriaNumber}`;

                const criteriaDiv = document.createElement('div');
                criteriaDiv.id = `criteria-item-${criteriaId}`;
                criteriaDiv.className = 'border border-gray-200 rounded-md p-3 mb-3';
                criteriaDiv.setAttribute('data-criteria-id', criteriaId);
                criteriaDiv.innerHTML = `
                    <div class="criteria-header mb-2">
                        <span class="text-sm font-medium">Criteria ${criteriaNumber}</span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <select class="criteria-section w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="">Select category...</option>
                            <option value="conditions">Conditions</option>
                            <option value="interventions_names">Interventions</option>
                            <option value="study_phase_category">Study Phase</option>
                            <option value="allocation_method">Allocation Method</option>
                            <option value="masking_quality_category">Masking Quality</option>
                            <option value="sponsor_type_category">Sponsor Type</option>
                            <option value="geographic_scope_category">Geographic Scope</option>
                            <option value="data_quality_category">Data Quality</option>
                            <option value="results_availability_category">Results Availability</option>
                        </select>
                        <input type="text" class="criteria-term w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Search term">
                        <button class="discover-criteria-btn px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" data-criteria-id="${criteriaId}">
                            Discover Terms
                        </button>
                    </div>
                    <!-- Status Display Area for Additional Criteria -->
                    <div class="criteria-discover-status mt-3 hidden p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div class="animate-pulse text-blue-600">🤖 Using AI to generate a comprehensive list...</div>
                    </div>
                    <div class="criteria-terms mt-3 hidden">
                        <!-- Terms will be populated here -->
                    </div>
                `;

                criteriaList.appendChild(criteriaDiv);

                // Show the Additional Criteria section when first criteria is added
                document.getElementById('additional-criteria-section-meta').classList.remove('hidden');

                // Add event listeners for the new buttons
                criteriaDiv.querySelector('.discover-criteria-btn').addEventListener('click', function() {
                    discoverCriteriaTermsMeta(this.getAttribute('data-criteria-id'));
                });
            }

            function resetCriteriaMeta() {
                currentCriteria = [];
                filteredStudies = [];
                criteriaIdCounter = 1;

                // Clear criteria list
                document.getElementById('criteria-list-meta').innerHTML = '';

                // Hide sections
                document.getElementById('additional-criteria-section-meta').classList.add('hidden');
                document.getElementById('filter-summary-meta').classList.add('hidden');
                document.getElementById('term-selection-meta').classList.add('hidden');

                // Hide meta-analysis panel (user is starting over from Filter Criteria)
                document.getElementById('meta-analysis-panel').classList.add('hidden');

                // Hide the action buttons row (will be shown after filtering is complete)
                document.getElementById('filter-action-buttons').classList.add('hidden');

                // Re-enable and restore styling for outcome section elements
                const sectionSelect = document.getElementById('outcome-section-select-meta');
                const searchTermInput = document.getElementById('outcome-term-meta');
                const discoverBtn = document.getElementById('discover-terms-btn-meta');
                const confirmBtn = document.getElementById('confirm-outcome-btn-meta');
                const termCheckboxes = document.querySelectorAll('#term-checkboxes-meta input');

                // Re-enable and restore styling for form elements
                sectionSelect.disabled = false;
                sectionSelect.classList.remove('bg-gray-100', 'cursor-not-allowed', 'text-gray-500');

                searchTermInput.disabled = false;
                searchTermInput.classList.remove('bg-gray-100', 'cursor-not-allowed', 'text-gray-500', 'opacity-75');

                discoverBtn.disabled = false;
                discoverBtn.classList.remove('opacity-50', 'cursor-not-allowed');

                confirmBtn.disabled = false;
                confirmBtn.classList.remove('opacity-50', 'cursor-not-allowed');

                // Re-enable all term checkboxes and restore their styling
                termCheckboxes.forEach(checkbox => {
                    checkbox.disabled = false;
                    checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
                });

                // Reset form values
                searchTermInput.value = '';
                sectionSelect.value = '';

                // Update status
                const studiesStatus = document.getElementById('studies-status-meta');
                studiesStatus.textContent = 'Select a module to filter for associated terms';
                studiesStatus.className = 'text-xs text-gray-500';
            }

            function removeCriteriaMeta(criteriaId) {
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                if (criteriaElement) {
                    criteriaElement.remove();
                }

                // Remove from currentCriteria array
                currentCriteria = currentCriteria.filter(c => c.id !== criteriaId);

                // Reapply filtering
                if (currentCriteria.length > 0) {
                    applyProgressiveFilteringMeta();
                }
            }

            function discoverCriteriaTermsMeta(criteriaId) {
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                const category = criteriaElement.querySelector('.criteria-section').value;
                const searchTerm = criteriaElement.querySelector('.criteria-term').value.trim();
                const discoverBtn = criteriaElement.querySelector('.discover-criteria-btn');
                const discoverStatus = criteriaElement.querySelector('.criteria-discover-status');
                const studiesDir = `output/${drugFolderSelect.value}`;

                if (!category || !searchTerm) {
                    alert('Please select a category and enter a search term');
                    return;
                }

                // Check if filteredStudies is available
                if (!filteredStudies || filteredStudies.length === 0) {
                    alert('Please complete the Outcome (Primary Filter) first by selecting terms and clicking "Confirm & Apply Filter".');
                    return;
                }

                // Hide button and show status message
                discoverBtn.classList.add('hidden');
                discoverStatus.classList.remove('hidden');

                console.log(`🔍 Discovering terms for "${searchTerm}" in ${category}...`);
                console.log(`📊 Searching within ${filteredStudies.length} filtered studies`);

                // Get AI model configuration for enhanced term discovery
                const aiConfig = getAiModelConfiguration();

                fetch('/api/enhanced-discover-terms', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        studies_dir: studiesDir,
                        category: category,
                        search_term: searchTerm,
                        ai_model: aiConfig.model || 'gpt-4',
                        filtered_studies: filteredStudies || null  // Pass filtered studies for progressive filtering
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCriteriaTermsMeta(criteriaId, data.results);
                    } else {
                        alert('Error discovering terms: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error);
                })
                .finally(() => {
                    // Show button again and hide status message
                    discoverBtn.classList.remove('hidden');
                    discoverStatus.classList.add('hidden');
                });
            }

            function displayCriteriaTermsMeta(criteriaId, results) {
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                const termsDiv = criteriaElement.querySelector('.criteria-terms');

                termsDiv.innerHTML = `
                    <label class="block text-sm font-medium text-gray-700 mb-2">Select Acceptable Terms:</label>
                    <div class="criteria-term-checkboxes grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
                        <!-- Terms will be populated here -->
                    </div>
                    <div class="mt-3 flex gap-2">
                        <button class="confirm-criteria-btn px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700" data-criteria-id="${criteriaId}">
                            Confirm
                        </button>
                    </div>
                `;

                const termCheckboxes = termsDiv.querySelector('.criteria-term-checkboxes');

                if (results.related_terms.length === 0) {
                    termCheckboxes.innerHTML = '<div class="text-gray-500 col-span-full text-center">No related terms found</div>';
                } else {
                    // Add the original search term as first option
                    const originalDiv = document.createElement('div');
                    originalDiv.className = 'flex items-center space-x-2';
                    const studiesText = results.studies_found === 1 ? 'study' : 'studies';
                    originalDiv.innerHTML = `
                        <input type="checkbox" id="criteria-term-${criteriaId}-${results.search_term}" value="${results.search_term}" class="rounded" checked>
                        <label for="criteria-term-${criteriaId}-${results.search_term}" class="text-sm cursor-pointer font-medium">
                            ${results.search_term} <span class="text-gray-500">(${results.studies_found} ${studiesText})</span>
                        </label>
                    `;
                    termCheckboxes.appendChild(originalDiv);

                    // Add related terms (server provides pre-formatted strings with study counts)
                    results.related_terms.forEach(termWithCount => {
                        // Extract just the term name for the ID (before the parentheses)
                        const termValue = termWithCount.split(' (')[0];
                        const div = document.createElement('div');
                        div.className = 'flex items-center space-x-2';
                        div.innerHTML = `
                            <input type="checkbox" id="criteria-term-${criteriaId}-${termValue}" value="${termWithCount}" class="rounded">
                            <label for="criteria-term-${criteriaId}-${termValue}" class="text-sm cursor-pointer">
                                ${termWithCount}
                            </label>
                        `;
                        termCheckboxes.appendChild(div);
                    });
                }

                termsDiv.classList.remove('hidden');

                // Add event listeners for confirm/cancel buttons
                termsDiv.querySelector('.confirm-criteria-btn').addEventListener('click', function() {
                    confirmCriteriaMeta(this.getAttribute('data-criteria-id'));
                });


            }

            function confirmCriteriaMeta(criteriaId) {
                const criteriaElement = document.getElementById(`criteria-item-${criteriaId}`);
                const selectedTerms = Array.from(criteriaElement.querySelectorAll('.criteria-term-checkboxes input:checked'))
                    .map(cb => cb.value);

                if (selectedTerms.length === 0) {
                    alert('Please select at least one term');
                    return;
                }

                const section = criteriaElement.querySelector('.criteria-section').value;
                const searchTerm = criteriaElement.querySelector('.criteria-term').value.trim();

                // Add criteria to the list
                const newCriteria = {
                    id: criteriaId,
                    section: section,
                    terms: selectedTerms,
                    type: 'additional',
                    original_term: searchTerm
                };

                // Add or update criteria
                const existingIndex = currentCriteria.findIndex(c => c.id === criteriaId);
                if (existingIndex >= 0) {
                    currentCriteria[existingIndex] = newCriteria;
                } else {
                    currentCriteria.push(newCriteria);
                }

                // Grey out and disable all form elements in this criteria
                const sectionSelect = criteriaElement.querySelector('.criteria-section');
                const searchTermInput = criteriaElement.querySelector('.criteria-term');
                const discoverBtn = criteriaElement.querySelector('.discover-criteria-btn');

                // Disable and grey out form elements
                sectionSelect.disabled = true;
                sectionSelect.classList.add('bg-gray-100', 'cursor-not-allowed', 'text-gray-500');

                searchTermInput.disabled = true;
                searchTermInput.classList.add('bg-gray-100', 'cursor-not-allowed', 'text-gray-500');

                discoverBtn.disabled = true;
                discoverBtn.classList.add('opacity-50', 'cursor-not-allowed');

                // Keep terms selection visible but make it read-only
                const termsElement = criteriaElement.querySelector('.criteria-terms');
                if (termsElement) {
                    // termsElement.classList.add('hidden'); // Commented out to keep terms visible
                    // Make checkboxes read-only but keep them visible
                    const checkboxes = termsElement.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.disabled = true;
                        checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
                    });

                    // Hide the confirm button since criteria is confirmed
                    const buttons = termsElement.querySelectorAll('.confirm-criteria-btn');
                    buttons.forEach(btn => btn.style.display = 'none');
                }

                criteriaElement.style.opacity = '0.8';
                // Don't disable pointer events completely, allow removal

                // Keep the criteria header as is - no status text changes

                // Apply progressive filtering
                applyProgressiveFilteringMeta();
            }



            // ========================================
            // NEW META-ANALYSIS FUNCTIONS
            // ========================================

            function confirmProjectMeta() {
                const drugFolderSelect = document.getElementById('drug-folder-meta');
                const selectedDrug = drugFolderSelect.value;

                if (!selectedDrug) {
                    alert('Please select a drug to analyze first');
                    return;
                }

                // Lock the dropdown and button
                drugFolderSelect.disabled = true;
                drugFolderSelect.classList.add('bg-gray-100', 'cursor-not-allowed');

                const confirmBtn = document.getElementById('confirm-project-meta-btn');
                confirmBtn.classList.add('hidden');

                // Update status
                const status = document.getElementById('drug-folder-meta-status');
                status.textContent = `✅ Project confirmed: ${selectedDrug}`;
                status.className = 'text-xs text-green-600 mt-1';

                console.log(`✅ Meta-analysis project confirmed: ${selectedDrug}`);
            }

            function confirmMetaAnalysis() {
                // Check if we have filtered studies
                if (!filteredStudies || filteredStudies.length === 0) {
                    alert('Please complete the filtering process first by setting up criteria and confirming filters.');
                    return;
                }

                const drugFolderSelect = document.getElementById('drug-folder-meta');
                const selectedDrug = drugFolderSelect.value;

                if (!selectedDrug) {
                    alert('Please confirm a project first');
                    return;
                }

                // Hide the action buttons row and show the meta-analysis panel
                const actionButtons = document.getElementById('filter-action-buttons');
                actionButtons.classList.add('hidden');

                const metaAnalysisPanel = document.getElementById('meta-analysis-panel');
                metaAnalysisPanel.classList.remove('hidden');

                // Update meta-analysis details
                const detailsDiv = document.getElementById('meta-analysis-details');
                detailsDiv.innerHTML = `
                    <div class="space-y-1">
                        <div><strong>Project:</strong> ${selectedDrug}</div>
                        <div><strong>Filtered Studies:</strong> ${filteredStudies.length} studies ready for analysis</div>
                        <div><strong>Status:</strong> Ready to prepare studies for R meta-analysis</div>
                    </div>
                `;

                console.log(`🚀 Meta-analysis confirmed for ${selectedDrug} with ${filteredStudies.length} studies`);
            }

            function prepareStudiesForAnalysis() {
                const drugFolderSelect = document.getElementById('drug-folder-meta');
                const selectedDrug = drugFolderSelect.value;

                if (!selectedDrug || !filteredStudies || filteredStudies.length === 0) {
                    alert('No filtered studies available for preparation');
                    return;
                }

                // Show progress
                const copyProgress = document.getElementById('copy-progress');
                const copyProgressBar = document.getElementById('copy-progress-bar');
                const copyStatus = document.getElementById('copy-status');
                const prepareBtn = document.getElementById('prepare-studies-btn');

                copyProgress.classList.remove('hidden');
                prepareBtn.disabled = true;
                prepareBtn.textContent = 'Preparing...';

                // Call backend to copy studies to output_meta folder
                fetch('/api/prepare-meta-analysis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        drug_folder: selectedDrug,
                        filtered_studies: filteredStudies
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        copyProgressBar.style.width = '100%';
                        copyStatus.textContent = `✅ ${data.studies_copied} studies copied to ${data.output_path}`;

                        // Hide prepare button and show analysis buttons
                        prepareBtn.classList.add('hidden');
                        document.getElementById('generate-table1-btn').classList.remove('hidden');
                        document.getElementById('run-meta-analysis-btn').classList.remove('hidden');

                        console.log(`✅ Studies prepared: ${data.studies_copied} studies copied to ${data.output_path}`);
                    } else {
                        copyStatus.textContent = `❌ Error: ${data.message}`;
                        prepareBtn.disabled = false;
                        prepareBtn.textContent = '📋 Prepare Studies for Analysis';
                    }
                })
                .catch(error => {
                    console.error('Error preparing studies:', error);
                    copyStatus.textContent = `❌ Error preparing studies: ${error.message}`;
                    prepareBtn.disabled = false;
                    prepareBtn.textContent = '📋 Prepare Studies for Analysis';
                });
            }

            function generateTable1() {
                const drugFolderSelect = document.getElementById('drug-folder-meta');
                const selectedDrug = drugFolderSelect.value;

                if (!selectedDrug) {
                    alert('No project selected');
                    return;
                }

                // Parse project and drug name
                const [projectName, drugName] = selectedDrug.includes('/')
                    ? selectedDrug.split('/', 2)
                    : ['default_project', selectedDrug];

                const generateBtn = document.getElementById('generate-table1-btn');
                generateBtn.disabled = true;
                generateBtn.textContent = 'Generating Table 1...';

                // Call backend to generate Table 1
                fetch('/api/generate-table1', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        project_name: projectName,
                        drug_name: drugName,
                        ai_model: 'llama2'  // You can make this configurable later
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`✅ Table 1 generated successfully! CSV saved to: ${data.csv_path}`);
                        console.log('📊 Table 1 generation completed successfully');
                    } else {
                        alert(`❌ Table 1 generation failed: ${data.message}`);
                        console.error('Table 1 error:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error generating Table 1:', error);
                    alert(`❌ Error generating Table 1: ${error.message}`);
                })
                .finally(() => {
                    generateBtn.disabled = false;
                    generateBtn.textContent = '📋 Generate Table 1';
                });
            }

            function runMetaAnalysis() {
                const drugFolderSelect = document.getElementById('drug-folder-meta');
                const selectedDrug = drugFolderSelect.value;

                if (!selectedDrug) {
                    alert('No project selected');
                    return;
                }

                // Parse project and drug name
                const [projectName, drugName] = selectedDrug.includes('/')
                    ? selectedDrug.split('/', 2)
                    : ['default_project', selectedDrug];

                const runBtn = document.getElementById('run-meta-analysis-btn');
                runBtn.disabled = true;
                runBtn.textContent = 'Running R Analysis...';

                // Call backend to run R meta-analysis
                fetch('/api/run-meta-analysis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        project_name: projectName,
                        drug_name: drugName
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`✅ Meta-analysis complete! PRISMA diagram saved to: ${data.output_path}`);
                        console.log('🔬 R Meta-analysis completed successfully');
                    } else {
                        alert(`❌ Meta-analysis failed: ${data.message}`);
                    }
                })
                .catch(error => {
                    console.error('Error running meta-analysis:', error);
                    alert(`❌ Error running meta-analysis: ${error.message}`);
                })
                .finally(() => {
                    runBtn.disabled = false;
                    runBtn.textContent = '📈 Run Meta-analysis';
                });
            }

            // AI Model functions
            function loadAiModels() {
                if (!aiModelSelect) return;

                aiModelSelect.innerHTML = '<option value="">Loading available models...</option>';
                modelStatus.textContent = 'Connecting to Ollama...';
                modelStatus.className = 'text-sm text-blue-600 mt-1';

                // Connect to server's Ollama proxy endpoint
                fetch('/api/models')
                    .then(response => response.json())
                    .then(data => {
                        if (data.models && data.models.length > 0) {
                            aiModelSelect.innerHTML = '<option value="">Select AI Model</option>';
                            data.models.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.name;
                                option.textContent = model.display; // Use the pre-formatted display name from server
                                aiModelSelect.appendChild(option);
                            });
                            modelStatus.textContent = `✅ Found ${data.models.length} models`;
                            modelStatus.className = 'text-sm text-green-600 mt-1';

                            // Load saved configuration
                            loadSavedConfiguration();
                        } else {
                            aiModelSelect.innerHTML = '<option value="">No models found</option>';
                            modelStatus.textContent = '❌ No models found in Ollama';
                            modelStatus.className = 'text-sm text-red-600 mt-1';
                        }
                    })
                    .catch(error => {
                        console.error('Error connecting to Ollama:', error);
                        aiModelSelect.innerHTML = '<option value="">Connection failed</option>';
                        modelStatus.textContent = '❌ Cannot connect to Ollama (http://localhost:11434)';
                        modelStatus.className = 'text-sm text-red-600 mt-1';
                    });
            }

            // formatSize function removed - server now provides pre-formatted display names

            function saveAiConfiguration() {
                if (!selectedAiModel) return;

                const config = {
                    model: selectedAiModel,
                    temperature: parseFloat(temperatureSetting.value),
                    maxTokens: parseInt(maxTokensSetting.value),
                    timestamp: new Date().toISOString()
                };

                localStorage.setItem('aiModelConfig', JSON.stringify(config));
            }

            function loadSavedConfiguration() {
                const savedConfig = localStorage.getItem('aiModelConfig');
                if (savedConfig) {
                    try {
                        const config = JSON.parse(savedConfig);

                        // Set model selection
                        if (config.model) {
                            aiModelSelect.value = config.model;
                            selectedAiModel = config.model;
                            modelSettings.classList.remove('hidden');
                        }

                        // Set temperature
                        if (config.temperature !== undefined) {
                            temperatureSetting.value = config.temperature;
                            temperatureValue.textContent = config.temperature;
                        }

                        // Set max tokens
                        if (config.maxTokens) {
                            maxTokensSetting.value = config.maxTokens;
                        }

                        updateConfigDisplay();
                    } catch (error) {
                        console.error('Error loading saved configuration:', error);
                    }
                }
            }

            function updateConfigDisplay() {
                if (!selectedAiModel) {
                    configDisplay.innerHTML = '<p>No model configured. Please select a model above.</p>';
                    return;
                }

                const temperature = temperatureSetting.value;
                const maxTokens = maxTokensSetting.value;

                configDisplay.innerHTML = `
                    <div class="space-y-2">
                        <p><span class="font-medium">Model:</span> ${selectedAiModel}</p>
                        <p><span class="font-medium">Temperature:</span> ${temperature} (${getTemperatureDescription(temperature)})</p>
                        <p><span class="font-medium">Max Tokens:</span> ${maxTokens}</p>
                        <p class="text-sm text-green-600 mt-3">✅ Configuration saved and ready for use</p>
                    </div>
                `;
            }

            function getTemperatureDescription(temp) {
                const t = parseFloat(temp);
                if (t <= 0.2) return 'Very focused, deterministic';
                if (t <= 0.5) return 'Balanced, reliable';
                if (t <= 0.8) return 'Creative, varied';
                return 'Highly creative, unpredictable';
            }

            // ========================================
            // MISSING INITIALIZATION FUNCTIONS
            // ========================================

            /**
             * Initialize tab switching functionality
             */
            function initializeTabs() {
                tabButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const tabId = this.id.replace('-tab', '-content');

                        // Remove active class from all tabs and contents
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        tabContents.forEach(content => content.classList.remove('active'));

                        // Add active class to clicked tab and corresponding content
                        this.classList.add('active');
                        document.getElementById(tabId).classList.add('active');

                        // Load drug folders when meta-analysis tab is clicked
                        if (this.id === 'meta-analysis-tab') {
                            loadDrugFolders();
                        }
                    });
                });
            }

            /**
             * Remove drug selection panel
             */
            function removeDrugSelection() {
                console.log('🔥 removeDrugSelection called');

                try {
                    // Find all drug selection panels (excluding the original one)
                    const allPanels = document.querySelectorAll('[id^="drug-selection-panel-"]');
                    console.log('Found panels:', allPanels.length);

                    if (allPanels.length > 0) {
                        // Remove the most recently added panel (highest counter number)
                        const lastPanel = allPanels[allPanels.length - 1];
                        console.log('Removing panel:', lastPanel.id);

                        // Remove the panel from DOM
                        lastPanel.remove();

                        console.log('➖ Panel removed successfully');
                    } else {
                        console.log('No panels to remove');
                    }
                } catch (error) {
                    console.error('Error removing panel:', error);
                }
            }

            // ========================================
            // 9. INITIALIZATION SEQUENCE
            // ========================================

            /**
             * Initialize all components in the correct order
             */
            function initializeApplication() {
                console.log('🚀 Clinical Trials Platform - Initializing application components...');

                try {
                    // Initialize core components
                    initializeTabs();
                    initializeAiModel();
                    initializeModelSettings();
                    initializeProjectManagement();
                    initializeRemoveDrug();
                    initializeMetaAnalysis();

                    // Load initial data
                    loadAiModels();
                    loadSavedConfiguration();

                    console.log('✅ Application initialization complete');
                } catch (error) {
                    console.error('❌ Application initialization failed:', error);
                }
            }

            // Start initialization
            initializeApplication();
        });
    </script>
</body>
</html>