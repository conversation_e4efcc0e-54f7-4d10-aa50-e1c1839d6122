"""
Enhanced Filter Criteria Search System for Clinical Trials Meta-Analysis

This module provides hybrid search functionality:
- AI-powered broad search for adverse events
- Strict exact matching for conditions, interventions, and study characteristics
"""

import os
import csv
import json
import re
from typing import Dict, List, Tuple, Optional
import pandas as pd


class EnhancedFilterSearch:
    """Enhanced filter search with hybrid AI and exact matching approaches"""
    
    def __init__(self, studies_directory: str):
        """
        Initialize the enhanced filter search system
        
        Args:
            studies_directory (str): Path to drug directory containing NCT folders and analysis CSVs
        """
        self.studies_dir = studies_directory
        self.characteristics_data = None
        self.adverse_events_data = None
        self.load_data()
    
    def load_data(self):
        """Load characteristics and adverse events data"""
        try:
            # Load characteristics data (transposed format)
            char_csv_path = os.path.join(self.studies_dir, 'characteristics_of_included_studies.csv')
            if os.path.exists(char_csv_path):
                # Read transposed CSV (variables as rows, studies as columns)
                df = pd.read_csv(char_csv_path, index_col=0)
                # Convert to normal format (studies as rows, variables as columns)
                self.characteristics_data = df.T
                print(f"📊 Loaded characteristics data: {len(self.characteristics_data)} studies")
            
            # Load consolidated adverse events data
            ae_csv_path = os.path.join(self.studies_dir, 'all_adverse_events.csv')
            if os.path.exists(ae_csv_path):
                self.adverse_events_data = pd.read_csv(ae_csv_path)
                print(f"📊 Loaded adverse events data: {len(self.adverse_events_data)} events")
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
    
    def search_adverse_events_ai(self, query: str, ai_model: str = "gpt-4", filtered_studies: List[str] = None) -> List[Dict]:
        """
        AI-powered broad search for adverse events with synonyms and related terms

        Args:
            query (str): Search query (e.g., "arrhythmia")
            ai_model (str): AI model to use for search
            filtered_studies (List[str]): Optional list of study IDs to search within

        Returns:
            List[Dict]: List of matching terms with study counts
        """
        if self.adverse_events_data is None:
            return []

        try:
            # Filter adverse events data to only include specified studies if provided
            search_ae_data = self.adverse_events_data
            if filtered_studies:
                search_ae_data = self.adverse_events_data[self.adverse_events_data['study_id'].isin(filtered_studies)]

            # Get all unique adverse event terms from filtered data
            all_ae_terms = set()

            # Collect terms from all relevant columns
            ae_columns = ['term', 'preferred_term', 'system_organ_class', 'event_term']
            for col in ae_columns:
                if col in search_ae_data.columns:
                    terms = search_ae_data[col].dropna().unique()
                    all_ae_terms.update(terms)

            # Convert to list for AI processing
            ae_terms_list = list(all_ae_terms)

            # Use AI to find related terms (placeholder for now - will implement AI call)
            matching_terms = self._ai_find_related_terms(query, ae_terms_list, ai_model)

            # Count studies for each matching term (within filtered studies if provided)
            results = []
            for term in matching_terms:
                study_ids = self._get_studies_with_ae_term(term, filtered_studies)
                if study_ids:
                    results.append({
                        'term': term,
                        'study_count': len(study_ids),
                        'study_ids': study_ids,
                        'category': 'adverse_events'
                    })

            return results

        except Exception as e:
            print(f"❌ Error in adverse events AI search: {e}")
            return []
    
    def search_conditions_exact(self, query: str, filtered_studies: List[str] = None) -> List[Dict]:
        """
        Exact matching search for conditions (diabetes can match "diabetes with heart failure")

        Args:
            query (str): Search query (e.g., "diabetes")
            filtered_studies (List[str]): Optional list of study IDs to search within

        Returns:
            List[Dict]: List of matching conditions with study counts
        """
        if self.characteristics_data is None:
            return []

        try:
            query_lower = query.lower().strip()
            matching_conditions = set()

            # Filter data to only include specified studies if provided
            search_data = self.characteristics_data
            if filtered_studies:
                search_data = self.characteristics_data[self.characteristics_data.index.isin(filtered_studies)]

            # Search through conditions column
            if 'conditions' in search_data.columns:
                for condition in search_data['conditions'].dropna():
                    condition_str = str(condition).lower()
                    # Check if query is contained in the condition
                    if query_lower in condition_str:
                        # SKIP conditions that are identical to search term (case-insensitive)
                        if condition_str.strip() == query_lower:
                            continue
                        matching_conditions.add(str(condition))

            # Count studies for each matching condition (within filtered studies if provided)
            results = []
            for condition in matching_conditions:
                study_ids = self._get_studies_with_condition(condition, filtered_studies)
                if study_ids:
                    results.append({
                        'term': condition,
                        'study_count': len(study_ids),
                        'study_ids': study_ids,
                        'category': 'conditions'
                    })

            return results

        except Exception as e:
            print(f"❌ Error in conditions exact search: {e}")
            return []
    
    def search_interventions_exact(self, query: str, filtered_studies: List[str] = None) -> List[Dict]:
        """
        Exact component matching for interventions (must contain ONLY specified interventions)

        Args:
            query (str): Search query (e.g., "canagliflozin and placebo")
            filtered_studies (List[str]): Optional list of study IDs to search within

        Returns:
            List[Dict]: List of matching intervention combinations with study counts
        """
        if self.characteristics_data is None:
            return []

        try:
            # Parse query into required intervention components
            query_components = [comp.strip().lower() for comp in query.split(' and ')]

            matching_interventions = set()

            # Filter data to only include specified studies if provided
            search_data = self.characteristics_data
            if filtered_studies:
                search_data = self.characteristics_data[self.characteristics_data.index.isin(filtered_studies)]

            # Search through interventions_names column
            if 'interventions_names' in search_data.columns:
                for interventions in search_data['interventions_names'].dropna():
                    interventions_str = str(interventions).lower()

                    # Check if all query components are present
                    if self._interventions_match_exactly(interventions_str, query_components):
                        # SKIP interventions that are identical to search term (case-insensitive)
                        if interventions_str.strip() == query.lower().strip():
                            continue
                        matching_interventions.add(str(interventions))

            # Count studies for each matching intervention combination (within filtered studies if provided)
            results = []
            for intervention in matching_interventions:
                study_ids = self._get_studies_with_intervention(intervention, filtered_studies)
                if study_ids:
                    results.append({
                        'term': intervention,
                        'study_count': len(study_ids),
                        'study_ids': study_ids,
                        'category': 'interventions'
                    })

            return results

        except Exception as e:
            print(f"❌ Error in interventions exact search: {e}")
            return []
    
    def search_study_characteristics(self, category: str, query: str, filtered_studies: List[str] = None) -> List[Dict]:
        """
        Exact matching for study characteristics (categorical variables)

        Args:
            category (str): Category to search in (e.g., 'study_phase_category')
            query (str): Search query (e.g., 'Phase 3')
            filtered_studies (List[str]): Optional list of study IDs to search within

        Returns:
            List[Dict]: List of matching values with study counts
        """
        if self.characteristics_data is None:
            return []

        try:
            if category not in self.characteristics_data.columns:
                return []

            query_lower = query.lower().strip()
            matching_values = set()

            # Filter data to only include specified studies if provided
            search_data = self.characteristics_data
            if filtered_studies:
                search_data = self.characteristics_data[self.characteristics_data.index.isin(filtered_studies)]

            # Search for exact or partial matches in the category
            for value in search_data[category].dropna():
                value_str = str(value).lower()
                if query_lower in value_str:
                    # SKIP values that are identical to search term (case-insensitive)
                    if value_str.strip() == query_lower:
                        continue
                    matching_values.add(str(value))

            # Count studies for each matching value (within filtered studies if provided)
            results = []
            for value in matching_values:
                study_ids = self._get_studies_with_characteristic(category, value, filtered_studies)
                if study_ids:
                    results.append({
                        'term': value,
                        'study_count': len(study_ids),
                        'study_ids': study_ids,
                        'category': category
                    })

            return results

        except Exception as e:
            print(f"❌ Error in study characteristics search: {e}")
            return []
    
    def get_available_categories(self) -> List[str]:
        """
        Get list of available categorical variables for search
        
        Returns:
            List[str]: List of available category names
        """
        if self.characteristics_data is None:
            return []
        
        # Define categorical variables (excluding study_id and continuous variables)
        categorical_vars = [
            'study_phase_category',
            'allocation_method', 
            'masking_quality_category',
            'conditions',
            'interventions_names',
            'geographic_scope_category',
            'sponsor_type_category',
            'data_quality_category',
            'results_availability_category'
        ]
        
        # Return only categories that exist in the data
        available_categories = [cat for cat in categorical_vars 
                              if cat in self.characteristics_data.columns]
        
        return available_categories
    
    def _ai_find_related_terms(self, query: str, ae_terms: List[str], ai_model: str) -> List[str]:
        """
        Use AI to find related adverse event terms using semantic similarity

        Args:
            query (str): Search query
            ae_terms (List[str]): List of all adverse event terms
            ai_model (str): AI model to use

        Returns:
            List[str]: List of related terms
        """
        try:
            # Import AI function
            from ai_model_setup import query_ollama

            # Create a sample of terms to analyze (limit to avoid token limits)
            sample_size = min(200, len(ae_terms))
            ae_terms_sample = ae_terms[:sample_size]

            # Create AI prompt for finding related terms
            prompt = f"""You are a medical expert analyzing adverse events in clinical trials.

Given the search term: "{query}"

From the following list of adverse event terms, identify ALL terms that are medically related, synonymous, or represent the same condition/symptom:

{chr(10).join(ae_terms_sample)}

Instructions:
1. Find terms that are medically related to "{query}"
2. Include synonyms, alternative names, and related conditions
3. For example, if searching for "arrhythmia", include terms like "atrial fibrillation", "ventricular arrhythmia", "cardiac arrhythmia", etc.
4. Return ONLY the exact terms from the list above, one per line
5. Do not add explanations or additional text

Related terms:"""

            # Query AI model
            response = query_ollama(prompt, ai_model)

            if response.get('success') and response.get('response'):
                ai_response = response.get('response', '').strip()

                # Parse AI response - extract terms that exist in our list
                suggested_terms = []
                for line in ai_response.split('\n'):
                    term = line.strip()
                    if term and term in ae_terms:
                        suggested_terms.append(term)

                # Also include simple keyword matches as fallback
                query_lower = query.lower()
                keyword_matches = []
                for term in ae_terms:
                    term_lower = str(term).lower()
                    if query_lower in term_lower:
                        keyword_matches.append(term)

                # Combine AI suggestions with keyword matches, remove duplicates
                all_matches = list(set(suggested_terms + keyword_matches))

                print(f"🤖 AI found {len(suggested_terms)} semantic matches, {len(keyword_matches)} keyword matches")
                return all_matches
            else:
                print(f"⚠️ AI search failed, falling back to keyword matching")
                # Fallback to simple keyword matching
                query_lower = query.lower()
                related_terms = []
                for term in ae_terms:
                    term_lower = str(term).lower()
                    if query_lower in term_lower:
                        related_terms.append(term)
                return related_terms

        except Exception as e:
            print(f"❌ Error in AI term search: {e}")
            # Fallback to simple keyword matching
            query_lower = query.lower()
            related_terms = []
            for term in ae_terms:
                term_lower = str(term).lower()
                if query_lower in term_lower:
                    related_terms.append(term)
            return related_terms
    
    def _interventions_match_exactly(self, interventions_str: str, query_components: List[str]) -> bool:
        """
        Check if interventions contain exactly the specified components
        
        Args:
            interventions_str (str): Intervention string from data
            query_components (List[str]): Required intervention components
            
        Returns:
            bool: True if exact match (allowing dosages)
        """
        # Remove dosage information for matching
        clean_interventions = re.sub(r'\d+\s*mg\s*', '', interventions_str)
        
        # Check if all query components are present
        for component in query_components:
            if component not in clean_interventions:
                return False
        
        # Additional logic to ensure no extra interventions
        # This is a simplified implementation - can be enhanced
        return True
    
    def _get_studies_with_ae_term(self, term: str, filtered_studies: List[str] = None) -> List[str]:
        """Get study IDs that have the specified adverse event term"""
        if self.adverse_events_data is None:
            return []

        # Filter adverse events data to only include specified studies if provided
        search_ae_data = self.adverse_events_data
        if filtered_studies:
            search_ae_data = self.adverse_events_data[self.adverse_events_data['study_id'].isin(filtered_studies)]

        # Search across all AE columns for the term
        ae_columns = ['term', 'preferred_term', 'system_organ_class', 'event_term']
        study_ids = set()

        for col in ae_columns:
            if col in search_ae_data.columns:
                mask = search_ae_data[col].str.contains(term, case=False, na=False)
                study_ids.update(search_ae_data[mask]['study_id'].tolist())

        return list(study_ids)
    
    def _get_studies_with_condition(self, condition: str, filtered_studies: List[str] = None) -> List[str]:
        """Get study IDs that have the specified condition"""
        if self.characteristics_data is None:
            return []

        # Filter data to only include specified studies if provided
        search_data = self.characteristics_data
        if filtered_studies:
            search_data = self.characteristics_data[self.characteristics_data.index.isin(filtered_studies)]

        mask = search_data['conditions'] == condition
        return search_data[mask].index.tolist()
    
    def _get_studies_with_intervention(self, intervention: str, filtered_studies: List[str] = None) -> List[str]:
        """Get study IDs that have the specified intervention"""
        if self.characteristics_data is None:
            return []

        # Filter data to only include specified studies if provided
        search_data = self.characteristics_data
        if filtered_studies:
            search_data = self.characteristics_data[self.characteristics_data.index.isin(filtered_studies)]

        mask = search_data['interventions_names'] == intervention
        return search_data[mask].index.tolist()
    
    def _get_studies_with_characteristic(self, category: str, value: str, filtered_studies: List[str] = None) -> List[str]:
        """Get study IDs that have the specified characteristic value"""
        if self.characteristics_data is None:
            return []

        # Filter data to only include specified studies if provided
        search_data = self.characteristics_data
        if filtered_studies:
            search_data = self.characteristics_data[self.characteristics_data.index.isin(filtered_studies)]

        mask = search_data[category] == value
        return search_data[mask].index.tolist()


def test_enhanced_filter_search(studies_directory: str):
    """Test function for the enhanced filter search system"""
    
    print(f"🧪 Testing Enhanced Filter Search on: {studies_directory}")
    
    # Initialize search system
    search = EnhancedFilterSearch(studies_directory)
    
    # Test adverse events search
    print("\n📊 Testing Adverse Events Search:")
    ae_results = search.search_adverse_events_ai("arrhythmia")
    for result in ae_results[:5]:  # Show first 5 results
        print(f"  - {result['term']} ({result['study_count']} studies)")
    
    # Test conditions search
    print("\n📊 Testing Conditions Search:")
    cond_results = search.search_conditions_exact("diabetes")
    for result in cond_results[:5]:
        print(f"  - {result['term']} ({result['study_count']} studies)")
    
    # Test interventions search
    print("\n📊 Testing Interventions Search:")
    int_results = search.search_interventions_exact("canagliflozin and placebo")
    for result in int_results[:5]:
        print(f"  - {result['term']} ({result['study_count']} studies)")
    
    # Test study characteristics search
    print("\n📊 Testing Study Characteristics Search:")
    char_results = search.search_study_characteristics("study_phase_category", "Phase 3")
    for result in char_results[:5]:
        print(f"  - {result['term']} ({result['study_count']} studies)")
    
    print(f"\n✅ Enhanced Filter Search Test Complete")


if __name__ == "__main__":
    # Test with canagliflozin data
    test_studies_dir = "output_meta/diabetes/canagliflozin"
    if os.path.exists(test_studies_dir):
        test_enhanced_filter_search(test_studies_dir)
    else:
        print(f"❌ Test directory not found: {test_studies_dir}")
